{"providers": {"ollama": {"host": "http://localhost:11434", "models": {"llama3.2:3b": {"role": "architect, chat, documentation", "agents": ["architect"], "description": "Great for planning, orchestration, chat, and documentation", "context_window": 8192, "performance": "balanced", "specialized_for": ["planning", "coordination", "documentation", "communication"]}, "starcoder2:3b": {"role": "frontend, code_completion", "agents": ["frontend"], "description": "Perfect for UI/UX, React, and fast code completion", "context_window": 16384, "performance": "high_quality", "specialized_for": ["ui_component", "code_completion", "quick_fixes"]}, "deepseek-coder:6.7b-instruct": {"role": "backend, debug, code_generation, code_review", "agents": ["backend", "debug", "issue_fix"], "description": "Superior for complex logic, structured generation, and code analysis", "context_window": 16384, "performance": "high_quality", "specialized_for": ["business_logic", "code_generation", "code_review", "debugging"]}, "qwen2.5:3b": {"role": "shell, test, code_analysis", "agents": ["shell", "test", "issue_fix"], "description": "Good for system commands, code analysis, and testing", "context_window": 32768, "performance": "balanced", "specialized_for": ["system_commands", "unit_testing", "error_analysis"]}, "yi-coder:1.5b": {"role": "code_completion", "agents": ["frontend", "backend"], "description": "Fast code completion and incremental development", "context_window": 4096, "performance": "fast", "specialized_for": ["code_completion", "syntax_checking", "quick_fixes"]}, "mistral:7b-instruct-q4_0": {"role": "general_chat", "agents": ["architect", "issue_fix"], "description": "General chat, explanations, and strategic planning", "context_window": 8192, "performance": "balanced", "specialized_for": ["requirements_analysis", "planning", "documentation"]}}}}, "routing": {"architect": {"primary": "llama3.2:3b", "secondary": "deepseek-coder:6.7b-instruct", "fallback": "qwen2.5:3b", "task_routing": {"planning": "llama3.2:3b", "architecture": "deepseek-coder:6.7b-instruct", "coordination": "llama3.2:3b"}}, "frontend": {"primary": "starcoder2:3b", "secondary": "yi-coder:1.5b", "fallback": "llama3.2:3b", "task_routing": {"quick_completion": "yi-coder:1.5b", "complex_components": "starcoder2:3b", "ui_logic": "starcoder2:3b"}}, "backend": {"primary": "deepseek-coder:6.7b-instruct", "secondary": "starcoder2:3b", "fallback": "qwen2.5:3b", "task_routing": {"api_design": "deepseek-coder:6.7b-instruct", "business_logic": "deepseek-coder:6.7b-instruct", "optimization": "qwen2.5:3b"}}, "shell": {"primary": "qwen2.5:3b", "secondary": "deepseek-coder:6.7b-instruct", "fallback": "llama3.2:3b", "task_routing": {"system_commands": "qwen2.5:3b", "deployment": "deepseek-coder:6.7b-instruct", "scripting": "qwen2.5:3b"}}, "debug": {"primary": "deepseek-coder:6.7b-instruct", "secondary": "qwen2.5:3b", "fallback": "llama3.2:3b", "task_routing": {"debugging": "deepseek-coder:6.7b-instruct", "error_analysis": "qwen2.5:3b", "explanation": "llama3.2:3b"}}, "test": {"primary": "qwen2.5:3b", "secondary": "deepseek-coder:6.7b-instruct", "fallback": "llama3.2:3b", "task_routing": {"unit_testing": "qwen2.5:3b", "test_strategy": "deepseek-coder:6.7b-instruct"}}, "issue_fix": {"primary": "deepseek-coder:6.7b-instruct", "secondary": "qwen2.5:3b", "fallback": "llama3.2:3b", "task_routing": {"debugging": "deepseek-coder:6.7b-instruct", "error_analysis": "qwen2.5:3b", "explanation": "llama3.2:3b"}}}, "embedding_models": {"ltkb": {"model": "nomic-embed-text:v1.5", "chunk_size": 2048, "chunk_overlap": 256, "description": "Long-context embeddings for LTKB knowledge management"}, "stpm": {"model": "mxbai-embed-large", "chunk_size": 512, "chunk_overlap": 64, "description": "Fast retrieval embeddings for Short-Term Project Memory"}}, "performance_settings": {"max_concurrent_requests": 3, "request_timeout": 180, "retry_attempts": 3, "backoff_factor": 1.5, "health_check_interval": 30, "gpu_warmup_timeout": 60, "cold_start_timeout": 120, "warm_model_timeout": 30}, "health_routing": {"unhealthy_model_timeout": 300, "auto_fallback_on_failure": true, "health_score_threshold": 0.8, "recovery_check_interval": 60, "max_consecutive_failures": 3, "initial_health_check_timeout": 60, "warm_health_check_timeout": 15, "health_check_retry_attempts": 2}, "task_timeouts": {"code_completion": 30, "code_generation": 90, "complex_generation": 180, "debugging": 120, "planning": 150, "system_commands": 60, "unit_testing": 90, "documentation": 120}, "quality_thresholds": {"minimum_confidence": 0.7, "context_relevance": 0.8, "response_completeness": 0.85, "code_syntax_validity": 0.95}, "model_quality_overrides": {"yi-coder:1.5b": {"minimum_confidence": 0.6, "context_relevance": 0.7, "response_completeness": 0.75, "description": "Lower thresholds for fast completion model"}, "deepseek-coder:6.7b-instruct": {"minimum_confidence": 0.8, "context_relevance": 0.85, "response_completeness": 0.9, "code_syntax_validity": 0.98, "description": "Higher thresholds for premium analysis model"}, "starcoder2:3b": {"minimum_confidence": 0.75, "code_syntax_validity": 0.97, "description": "High code quality expectations for frontend work"}, "qwen2.5:3b": {"context_relevance": 0.85, "description": "Leverage large context window effectively"}}, "load_balancing": {"strategy": "round_robin", "health_weight": 0.4, "performance_weight": 0.3, "availability_weight": 0.3, "min_healthy_models": 2, "circuit_breaker_threshold": 5}, "model_analytics": {"track_performance": true, "track_quality_scores": true, "track_usage_patterns": true, "retention_days": 30, "aggregate_metrics": {"response_time_p95": true, "quality_score_avg": true, "error_rate": true, "throughput": true}}, "adaptive_routing": {"enabled": true, "learning_rate": 0.1, "quality_decay_factor": 0.95, "performance_window_minutes": 60, "min_samples_for_adaptation": 10}}