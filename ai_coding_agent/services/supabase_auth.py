"""
Supabase Authentication Service for AI Coding Agent.

This module integrates Supabase Auth with the existing JWT authentication system,
providing unified user management across local and cloud databases.
"""

from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Union
import asyncio
from dataclasses import dataclass

from fastapi import HTTPException, status
from supabase import AuthError, AuthApiError

from ..services.supabase import SupabaseService
from ..config import settings
from ..models.user import User, UserCreate, UserResponse, UserLogin

@dataclass
class AuthTokens:
    """Authentication tokens from Supabase."""
    access_token: str
    refresh_token: str
    expires_in: int
    token_type: str = "bearer"

@dataclass
class SupabaseUser:
    """Supabase user data."""
    id: str
    email: str
    email_confirmed_at: Optional[str] = None
    phone: Optional[str] = None
    created_at: str = ""
    updated_at: str = ""
    user_metadata: Optional[Dict[str, Any]] = None
    app_metadata: Optional[Dict[str, Any]] = None

class SupabaseAuthService:
    """
    Supabase authentication service that integrates with local user management.

    Features:
    - User registration and login via Supabase Auth
    - Local user profile synchronization
    - JWT token management
    - Password reset and email verification
    - Social authentication (Google, GitHub, etc.)
    """

    def __init__(self):
        """Initialize Supabase auth service."""
        self.supabase_service = SupabaseService()
        self.client = self.supabase_service.client

    async def register_user(
        self,
        email: str,
        password: str,
        user_data: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        Register a new user with Supabase Auth.

        Args:
            email: User's email address
            password: User's password
            user_data: Additional user metadata

        Returns:
            Dict containing user info and auth tokens
        """
        try:
            # Register with Supabase Auth
            auth_response = self.client.auth.sign_up({
                "email": email,
                "password": password,
                "options": {
                    "data": user_data or {}
                }
            })

            if auth_response.user is None:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Failed to create user account"
                )

            # Create local user profile
            await self._sync_user_to_local(auth_response.user)

            return {
                "user": self._format_user_response(auth_response.user),
                "session": self._format_session_response(auth_response.session) if auth_response.session else None,
                "message": "User registered successfully. Please check your email for verification."
            }

        except AuthApiError as e:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Registration failed: {e.message}"
            )
        except Exception as e:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Registration error: {str(e)}"
            )

    async def login_user(
        self,
        email: str,
        password: str
    ) -> Dict[str, Any]:
        """
        Authenticate user with Supabase Auth.

        Args:
            email: User's email address
            password: User's password

        Returns:
            Dict containing user info and auth tokens
        """
        try:
            # Authenticate with Supabase
            auth_response = self.client.auth.sign_in_with_password({
                "email": email,
                "password": password
            })

            if auth_response.user is None or auth_response.session is None:
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="Invalid email or password"
                )

            # Sync user data to local database
            await self._sync_user_to_local(auth_response.user)

            return {
                "user": self._format_user_response(auth_response.user),
                "session": self._format_session_response(auth_response.session),
                "message": "Login successful"
            }

        except AuthApiError as e:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail=f"Authentication failed: {e.message}"
            )
        except Exception as e:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Login error: {str(e)}"
            )

    async def logout_user(self, access_token: str) -> Dict[str, str]:
        """
        Logout user and invalidate session.

        Args:
            access_token: User's access token

        Returns:
            Dict with logout confirmation
        """
        try:
            # Set the session for logout
            self.client.auth.set_session(access_token, "")

            # Sign out from Supabase
            self.client.auth.sign_out()

            return {"message": "Logout successful"}

        except Exception as e:
            # Even if Supabase logout fails, we should succeed locally
            return {"message": "Logout completed"}

    async def refresh_token(self, refresh_token: str) -> Dict[str, Any]:
        """
        Refresh authentication tokens.

        Args:
            refresh_token: User's refresh token

        Returns:
            Dict with new tokens
        """
        try:
            auth_response = self.client.auth.refresh_session(refresh_token)

            if auth_response.session is None:
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="Invalid refresh token"
                )

            return {
                "session": self._format_session_response(auth_response.session),
                "message": "Tokens refreshed successfully"
            }

        except AuthApiError as e:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail=f"Token refresh failed: {e.message}"
            )

    async def get_user_by_token(self, access_token: str) -> Optional[SupabaseUser]:
        """
        Get user information from access token.

        Args:
            access_token: User's access token

        Returns:
            SupabaseUser object or None
        """
        try:
            # Set the session
            self.client.auth.set_session(access_token, "")

            # Get user
            user_response = self.client.auth.get_user()

            if user_response is None or not hasattr(user_response, 'user') or user_response.user is None:
                return None

            return self._format_supabase_user(user_response.user)

        except Exception:
            return None

    async def reset_password(self, email: str) -> Dict[str, str]:
        """
        Send password reset email.

        Args:
            email: User's email address

        Returns:
            Dict with confirmation message
        """
        try:
            self.client.auth.reset_password_email(email)

            return {
                "message": "Password reset email sent. Please check your inbox."
            }

        except AuthApiError as e:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Password reset failed: {e.message}"
            )

    async def update_user_metadata(
        self,
        access_token: str,
        user_metadata: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Update user metadata.

        Args:
            access_token: User's access token
            user_metadata: Metadata to update

        Returns:
            Dict with updated user info
        """
        try:
            # Set the session
            self.client.auth.set_session(access_token, "")

            # Update user
            user_response = self.client.auth.update_user({
                "data": user_metadata
            })

            if user_response.user is None:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Failed to update user"
                )

            # Sync to local database
            await self._sync_user_to_local(user_response.user)

            return {
                "user": self._format_user_response(user_response.user),
                "message": "User updated successfully"
            }

        except AuthApiError as e:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Update failed: {e.message}"
            )

    async def _sync_user_to_local(self, supabase_user) -> None:
        """
        Synchronize Supabase user to local database.

        Args:
            supabase_user: Supabase user object
        """
        try:
            from ..models.base import get_hybrid_db_manager
            from ..models.user import User

            manager = get_hybrid_db_manager()

            # Get database session
            db = next(manager.get_local_db())
            try:
                # Check if user exists locally
                local_user = db.query(User).filter(User.supabase_id == supabase_user.id).first()

                if local_user is None:
                    # Create new local user
                    local_user = User(
                        supabase_id=supabase_user.id,
                        email=supabase_user.email,
                        username=supabase_user.user_metadata.get("username", supabase_user.email.split("@")[0]) if supabase_user.user_metadata else supabase_user.email.split("@")[0],
                        full_name=supabase_user.user_metadata.get("full_name", "") if supabase_user.user_metadata else "",
                        is_active=True,
                        created_at=datetime.fromisoformat(supabase_user.created_at.replace("Z", "+00:00")),
                        updated_at=datetime.utcnow()
                    )
                    db.add(local_user)
                else:
                    # Update existing user
                    local_user.email = supabase_user.email
                    local_user.updated_at = datetime.utcnow()

                    # Update metadata if provided
                    if supabase_user.user_metadata:
                        if "username" in supabase_user.user_metadata:
                            local_user.username = supabase_user.user_metadata["username"]
                        if "full_name" in supabase_user.user_metadata:
                            local_user.full_name = supabase_user.user_metadata["full_name"]

                db.commit()
            finally:
                db.close()

        except Exception as e:
            # Log error but don't fail auth operation
            print(f"Warning: Failed to sync user to local database: {e}")

    def _format_user_response(self, supabase_user) -> Dict[str, Any]:
        """Format Supabase user for API response."""
        return {
            "id": supabase_user.id,
            "email": supabase_user.email,
            "email_confirmed_at": supabase_user.email_confirmed_at,
            "phone": supabase_user.phone,
            "created_at": supabase_user.created_at,
            "updated_at": supabase_user.updated_at,
            "user_metadata": supabase_user.user_metadata or {},
            "app_metadata": supabase_user.app_metadata or {}
        }

    def _format_session_response(self, session) -> Dict[str, Any]:
        """Format Supabase session for API response."""
        return {
            "access_token": session.access_token,
            "refresh_token": session.refresh_token,
            "expires_in": session.expires_in,
            "expires_at": session.expires_at,
            "token_type": session.token_type,
            "user": self._format_user_response(session.user) if session.user else None
        }

    def _format_supabase_user(self, user) -> SupabaseUser:
        """Format Supabase user object."""
        return SupabaseUser(
            id=user.id,
            email=user.email,
            email_confirmed_at=user.email_confirmed_at,
            phone=user.phone,
            created_at=user.created_at,
            updated_at=user.updated_at,
            user_metadata=user.user_metadata or {},
            app_metadata=user.app_metadata or {}
        )

# Singleton instance
_supabase_auth_service = None

def get_supabase_auth_service() -> SupabaseAuthService:
    """Get or create Supabase auth service instance."""
    global _supabase_auth_service
    if _supabase_auth_service is None:
        _supabase_auth_service = SupabaseAuthService()
    return _supabase_auth_service
