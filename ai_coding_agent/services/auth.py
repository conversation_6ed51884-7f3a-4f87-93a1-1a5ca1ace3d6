"""
Core JWT and authentication utilities for AI Coding Agent.

This module provides shared authentication utilities used by both
legacy local auth and modern Supabase auth systems.

IMPORTANT: This is now a utility module. For user authentication,
use supabase_auth.py instead.
"""

from datetime import datetime, timedelta
from typing import Optional, Union

from fastapi import Depends, HTTPException, status
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from jose import JWTError, jwt
from passlib.context import Crypt<PERSON>ontext
from sqlalchemy.orm import Session

from ..config import settings
from ..models import get_db, User

# Password hashing context
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

# HTTP Bearer token scheme
security = HTT<PERSON>Bearer()


def verify_password(plain_password: str, hashed_password: str) -> bool:
    """
    Verify a plaintext password against its hash.

    Args:
        plain_password: The plaintext password to verify
        hashed_password: The stored password hash

    Returns:
        bool: True if password matches, False otherwise
    """
    return pwd_context.verify(plain_password, hashed_password)


def get_password_hash(password: str) -> str:
    """
    Hash a plaintext password using bcrypt.

    Args:
        password: The plaintext password to hash

    Returns:
        str: The hashed password
    """
    return pwd_context.hash(password)


def create_access_token(data: dict, expires_delta: Optional[timedelta] = None, is_admin: bool = False) -> str:
    """
    Create a JWT access token with role-based expiration.

    Args:
        data: The data to encode in the token
        expires_delta: Optional custom expiration time
        is_admin: Whether this is an admin token (shorter expiration)

    Returns:
        str: The encoded JWT token
    """
    to_encode = data.copy()

    if expires_delta:
        expire = datetime.utcnow() + expires_delta
    elif is_admin:
        # Admin tokens expire in 15 minutes for enhanced security
        expire = datetime.utcnow() + timedelta(minutes=15)
        to_encode.update({"role": "admin", "admin_session": True})
    else:
        # Regular user tokens use default expiration
        expire = datetime.utcnow() + timedelta(minutes=settings.security.access_token_expire_minutes)

    to_encode.update({
        "exp": expire,
        "iat": datetime.utcnow(),  # Issued at time
        "token_type": "admin_access" if is_admin else "access"
    })

    encoded_jwt = jwt.encode(to_encode, settings.security.secret_key, algorithm=settings.security.algorithm)
    return encoded_jwt


def create_admin_access_token(data: dict) -> str:
    """
    Create a short-lived admin access token (15 minutes).

    Args:
        data: The data to encode in the token

    Returns:
        str: The encoded JWT admin token
    """
    return create_access_token(data, is_admin=True)


def create_refresh_token(data: dict) -> str:
    """
    Create a JWT refresh token.

    Args:
        data: The data to encode in the token

    Returns:
        str: The encoded JWT refresh token
    """
    to_encode = data.copy()
    expire = datetime.utcnow() + timedelta(days=settings.security.refresh_token_expire_days)
    to_encode.update({"exp": expire})
    encoded_jwt = jwt.encode(to_encode, settings.security.secret_key, algorithm=settings.security.algorithm)
    return encoded_jwt


def verify_token(token: str, require_admin: bool = False) -> Optional[dict]:
    """
    Verify and decode a JWT token with optional admin validation.

    Args:
        token: The JWT token to verify
        require_admin: Whether to require admin token type

    Returns:
        dict: The decoded token payload, or None if invalid
    """
    try:
        payload = jwt.decode(token, settings.security.secret_key, algorithms=[settings.security.algorithm])

        # If admin token is required, validate token type
        if require_admin:
            token_type = payload.get("token_type")
            admin_session = payload.get("admin_session", False)

            if token_type != "admin_access" or not admin_session:
                return None

        return payload
    except JWTError:
        return None


def verify_admin_token(token: str) -> Optional[dict]:
    """
    Verify an admin token specifically.

    Args:
        token: The JWT token to verify

    Returns:
        dict: The decoded token payload, or None if invalid
    """
    return verify_token(token, require_admin=True)


def get_user_by_username(db: Session, username: str) -> Optional[User]:
    """
    Get user by username or email.

    Args:
        db: Database session
        username: Username or email to search for

    Returns:
        User: The user object, or None if not found
    """
    user = db.query(User).filter(User.username == username).first()
    if not user:
        user = db.query(User).filter(User.email == username).first()
    return user


def authenticate_user(db: Session, username: str, password: str) -> Union[User, bool]:
    """
    Authenticate a user with username/email and password.

    Args:
        db: Database session
        username: Username or email
        password: Plaintext password

    Returns:
        User: The authenticated user, or False if authentication fails
    """
    user = get_user_by_username(db, username)
    if not user:
        return False
    if not verify_password(password, str(user.hashed_password)):
        return False
    return user


async def get_current_user(
    credentials: HTTPAuthorizationCredentials = Depends(security),
    db: Session = Depends(get_db)
) -> User:
    """
    Get the current authenticated user from JWT token.

    This is a FastAPI dependency that extracts and validates
    the JWT token from the Authorization header.

    Args:
        credentials: HTTP Bearer credentials from request
        db: Database session

    Returns:
        User: The current authenticated user

    Raises:
        HTTPException: If token is invalid or user not found
    """
    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="Could not validate credentials",
        headers={"WWW-Authenticate": "Bearer"},
    )

    try:
        payload = verify_token(credentials.credentials)
        if payload is None:
            raise credentials_exception

        username = payload.get("sub")
        if username is None or not isinstance(username, str):
            raise credentials_exception

    except JWTError:
        raise credentials_exception

    user = get_user_by_username(db, username)
    if user is None:
        raise credentials_exception

    return user


async def get_current_active_user(current_user: User = Depends(get_current_user)) -> User:
    """
    Get the current active user (not disabled).

    Args:
        current_user: The current authenticated user

    Returns:
        User: The current active user

    Raises:
        HTTPException: If user is inactive
    """
    if not getattr(current_user, 'is_active', False):
        raise HTTPException(status_code=400, detail="Inactive user")
    return current_user


async def get_current_superuser(current_user: User = Depends(get_current_user)) -> User:
    """
    Get the current superuser.

    Args:
        current_user: The current authenticated user

    Returns:
        User: The current superuser

    Raises:
        HTTPException: If user is not a superuser
    """
    if not getattr(current_user, 'is_superuser', False):
        raise HTTPException(
            status_code=400, detail="The user doesn't have enough privileges"
        )
    return current_user
