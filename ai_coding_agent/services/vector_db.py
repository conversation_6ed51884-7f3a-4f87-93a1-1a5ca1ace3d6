"""
Vector Database Infrastructure for LTKB and STPM.

This module provides the vector database infrastructure using Chroma for storing
and retrieving embeddings for both LTKB (Long-Term Knowledge Base) and
STPM (Short-Term Project Memory).
"""

import os
import logging
import uuid
from datetime import datetime
from pathlib import Path
from typing import List, Dict, Any, Optional, Tuple, Union
from enum import Enum

try:
    import chromadb
    from chromadb.config import Settings
    from chromadb import PersistentClient
    CHROMADB_AVAILABLE = True
except ImportError:
    CHROMADB_AVAILABLE = False
    chromadb = None
    Settings = None
    PersistentClient = None

import httpx
from pydantic import BaseModel, Field, ConfigDict

from ..config import settings

logger = logging.getLogger(__name__)


class EmbeddingNamespace(str, Enum):
    """Namespaces for different types of embeddings."""
    LTKB = "ltkb"           # Long-term knowledge base
    STPM = "stpm"           # Short-term project memory
    SYSTEMS = "systems"     # System templates and patterns
    PROJECTS = "projects"   # Project-specific knowledge


class DocumentChunk(BaseModel):
    """Represents a document chunk for embedding."""

    id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    content: str = Field(description="Chunk content")
    source_document_id: str = Field(description="Source document ID")
    chunk_index: int = Field(description="Index of chunk within document")

    # Metadata
    metadata: Dict[str, Any] = Field(default_factory=dict)
    namespace: EmbeddingNamespace = Field(description="Embedding namespace")

    # Timestamps
    created_at: datetime = Field(default_factory=datetime.now)

    model_config = ConfigDict(use_enum_values=True)


class SearchResult(BaseModel):
    """Vector search result."""

    chunk: DocumentChunk
    similarity_score: float = Field(ge=0.0, le=1.0, description="Similarity score")
    distance: float = Field(description="Vector distance")


class EmbeddingConfig(BaseModel):
    """Configuration for embedding models."""

    model_name: str
    chunk_size: int
    chunk_overlap: int
    description: str

    model_config = ConfigDict(protected_namespaces=())


class VectorDBClient:
    """Vector database client using Chroma."""

    def __init__(self, persist_directory: Optional[str] = None):
        if not CHROMADB_AVAILABLE:
            raise RuntimeError("ChromaDB not available. Install with: pip install chromadb")

        self.persist_directory = persist_directory or "vector_db"
        Path(self.persist_directory).mkdir(parents=True, exist_ok=True)

        # Initialize Chroma client
        if not CHROMADB_AVAILABLE or chromadb is None or Settings is None:
            raise RuntimeError("ChromaDB not available. Install with: pip install chromadb")

        self.client = chromadb.PersistentClient(
            path=self.persist_directory,
            settings=Settings(
                anonymized_telemetry=False,
                allow_reset=True
            )
        )

        # Initialize collections for different namespaces
        self.collections = {}
        self._initialize_collections()

        # HTTP client for embedding requests
        self.http_client = httpx.AsyncClient(timeout=60.0)

        # Load embedding configurations
        self.embedding_configs = self._load_embedding_configs()

    def _initialize_collections(self):
        """Initialize Chroma collections for each namespace."""
        for namespace in EmbeddingNamespace:
            collection_name = f"ai_coding_agent_{namespace.value}"

            try:
                # Try to get existing collection
                collection = self.client.get_collection(name=collection_name)
            except Exception:
                # Create new collection if it doesn't exist
                collection = self.client.create_collection(
                    name=collection_name,
                    metadata={"namespace": namespace.value}
                )

            self.collections[namespace] = collection
            logger.info(f"Initialized collection: {collection_name}")

    def _load_embedding_configs(self) -> Dict[str, EmbeddingConfig]:
        """Load embedding model configurations."""
        # Try multiple paths for models_config.json
        config_paths = [
            Path(__file__).parent / "models_config.json",
            Path(__file__).parent.parent / "models_config.json",
            Path(__file__).parent.parent.parent.parent / "src" / "ai_coding_agent" / "models_config.json"
        ]

        for config_path in config_paths:
            try:
                if config_path.exists():
                    import json
                    with open(config_path, 'r') as f:
                        config = json.load(f)

                    embedding_models = config.get("embedding_models", {})

                    configs = {}
                    for key, model_config in embedding_models.items():
                        configs[key] = EmbeddingConfig(
                            model_name=model_config["model"],
                            chunk_size=model_config["chunk_size"],
                            chunk_overlap=model_config["chunk_overlap"],
                            description=model_config["description"]
                        )

                    return configs
            except Exception as e:
                logger.debug(f"Failed to load config from {config_path}: {e}")
                continue

        logger.warning("Could not load embedding configs from any path, using defaults")
        # Return default configs
        return {
            "ltkb": EmbeddingConfig(
                model_name="nomic-embed-text:v1.5",
                chunk_size=2048,
                chunk_overlap=256,
                description="Long-context embeddings for LTKB"
            ),
            "stpm": EmbeddingConfig(
                model_name="mxbai-embed-large",
                chunk_size=512,
                chunk_overlap=64,
                description="Fast retrieval embeddings for STPM"
            )
        }

    async def generate_embedding(self, text: str, model_name: str) -> List[float]:
        """Generate embedding for text using specified model."""
        try:
            ollama_url = settings.ai.ollama_host
            response = await self.http_client.post(
                f"{ollama_url}/api/embeddings",
                json={
                    "model": model_name,
                    "prompt": text
                }
            )

            if response.status_code != 200:
                raise Exception(f"Ollama embedding request failed: {response.status_code}")

            result = response.json()
            return result.get("embedding", [])

        except Exception as e:
            logger.error(f"Error generating embedding with {model_name}: {e}")
            raise

    def chunk_document(self, content: str, config: EmbeddingConfig) -> List[str]:
        """Chunk document content based on configuration."""
        chunks = []
        chunk_size = config.chunk_size
        overlap = config.chunk_overlap

        # Simple character-based chunking
        start = 0
        while start < len(content):
            end = start + chunk_size
            chunk = content[start:end]

            # Try to break at word boundaries
            if end < len(content) and not content[end].isspace():
                last_space = chunk.rfind(' ')
                if last_space > chunk_size * 0.7:  # Don't make chunks too small
                    chunk = chunk[:last_space]
                    end = start + last_space

            chunks.append(chunk.strip())
            start = end - overlap

            if start >= len(content):
                break

        return chunks

    async def add_document(
        self,
        document_id: str,
        content: str,
        namespace: EmbeddingNamespace,
        metadata: Optional[Dict[str, Any]] = None,
        embedding_type: str = "ltkb"
    ) -> List[DocumentChunk]:
        """Add a document to the vector database."""

        if namespace not in self.collections:
            raise ValueError(f"Unknown namespace: {namespace}")

        if embedding_type not in self.embedding_configs:
            raise ValueError(f"Unknown embedding type: {embedding_type}")

        config = self.embedding_configs[embedding_type]
        collection = self.collections[namespace]

        # Chunk the document
        chunks_text = self.chunk_document(content, config)

        # Create document chunks
        document_chunks = []
        chunk_ids = []
        chunk_embeddings = []
        chunk_metadatas = []
        chunk_documents = []

        for i, chunk_text in enumerate(chunks_text):
            # Create chunk object
            chunk = DocumentChunk(
                content=chunk_text,
                source_document_id=document_id,
                chunk_index=i,
                namespace=namespace,
                metadata=metadata or {}
            )

            # Generate embedding
            embedding = await self.generate_embedding(chunk_text, config.model_name)

            # Prepare for batch insert
            chunk_ids.append(chunk.id)
            chunk_embeddings.append(embedding)
            chunk_metadatas.append({
                "source_document_id": document_id,
                "chunk_index": i,
                "namespace": namespace.value,
                "created_at": chunk.created_at.isoformat(),
                **chunk.metadata
            })
            chunk_documents.append(chunk_text)

            document_chunks.append(chunk)

        # Add to Chroma collection
        collection.add(
            ids=chunk_ids,
            embeddings=chunk_embeddings,
            metadatas=chunk_metadatas,
            documents=chunk_documents
        )

        logger.info(f"Added {len(document_chunks)} chunks for document {document_id} to {namespace.value}")
        return document_chunks

    async def search_similar(
        self,
        query: str,
        namespace: EmbeddingNamespace,
        limit: int = 10,
        embedding_type: str = "ltkb",
        filters: Optional[Dict[str, Any]] = None
    ) -> List[SearchResult]:
        """Search for similar content in the vector database."""

        if namespace not in self.collections:
            raise ValueError(f"Unknown namespace: {namespace}")

        if embedding_type not in self.embedding_configs:
            raise ValueError(f"Unknown embedding type: {embedding_type}")

        config = self.embedding_configs[embedding_type]
        collection = self.collections[namespace]

        # Generate query embedding
        query_embedding = await self.generate_embedding(query, config.model_name)

        # Prepare where clause for filtering
        where_clause = {}
        if filters:
            where_clause.update(filters)

        # Search in collection
        results = collection.query(
            query_embeddings=[query_embedding],
            n_results=limit,
            where=where_clause if where_clause else None
        )

        # Convert to SearchResult objects
        search_results = []

        if results['ids'] and results['ids'][0]:
            for i in range(len(results['ids'][0])):
                chunk_id = results['ids'][0][i]
                chunk_content = results['documents'][0][i]
                chunk_metadata = results['metadatas'][0][i]
                distance = results['distances'][0][i] if results['distances'] else 0.0

                # Convert distance to similarity score (assuming cosine distance)
                similarity_score = max(0.0, 1.0 - distance)

                # Create DocumentChunk
                chunk = DocumentChunk(
                    id=chunk_id,
                    content=chunk_content,
                    source_document_id=chunk_metadata.get('source_document_id', ''),
                    chunk_index=chunk_metadata.get('chunk_index', 0),
                    namespace=EmbeddingNamespace(chunk_metadata.get('namespace', namespace.value)),
                    metadata={k: v for k, v in chunk_metadata.items()
                             if k not in ['source_document_id', 'chunk_index', 'namespace', 'created_at']}
                )

                search_results.append(SearchResult(
                    chunk=chunk,
                    similarity_score=similarity_score,
                    distance=distance
                ))

        return search_results

    async def delete_document(self, document_id: str, namespace: EmbeddingNamespace) -> int:
        """Delete all chunks for a document from the vector database."""

        if namespace not in self.collections:
            raise ValueError(f"Unknown namespace: {namespace}")

        collection = self.collections[namespace]

        # Find all chunks for this document
        results = collection.get(
            where={"source_document_id": document_id}
        )

        if results['ids']:
            # Delete chunks
            collection.delete(ids=results['ids'])
            deleted_count = len(results['ids'])
            logger.info(f"Deleted {deleted_count} chunks for document {document_id} from {namespace.value}")
            return deleted_count

        return 0

    async def get_collection_stats(self, namespace: EmbeddingNamespace) -> Dict[str, Any]:
        """Get statistics for a collection."""

        if namespace not in self.collections:
            raise ValueError(f"Unknown namespace: {namespace}")

        collection = self.collections[namespace]

        # Get collection count
        count = collection.count()

        # Get sample of metadata to understand the collection
        sample_results = collection.get(limit=100)

        # Analyze metadata
        unique_documents = set()
        metadata_keys = set()

        if sample_results['metadatas']:
            for metadata in sample_results['metadatas']:
                if 'source_document_id' in metadata:
                    unique_documents.add(metadata['source_document_id'])
                metadata_keys.update(metadata.keys())

        return {
            "namespace": namespace.value,
            "total_chunks": count,
            "unique_documents": len(unique_documents),
            "sample_size": len(sample_results['ids']) if sample_results['ids'] else 0,
            "metadata_keys": list(metadata_keys)
        }

    async def close(self):
        """Close the HTTP client."""
        await self.http_client.aclose()


class EmbeddingAgent:
    """High-level embedding agent for LTKB operations."""

    def __init__(self, vector_db: Optional[VectorDBClient] = None):
        self.vector_db = vector_db or VectorDBClient()

    async def embed_ltkb_document(
        self,
        document_id: str,
        content: str,
        metadata: Optional[Dict[str, Any]] = None
    ) -> List[DocumentChunk]:
        """Embed a document for LTKB with long-context embeddings."""

        return await self.vector_db.add_document(
            document_id=document_id,
            content=content,
            namespace=EmbeddingNamespace.LTKB,
            metadata=metadata,
            embedding_type="ltkb"
        )

    async def embed_stpm_content(
        self,
        content_id: str,
        content: str,
        project_id: str,
        metadata: Optional[Dict[str, Any]] = None
    ) -> List[DocumentChunk]:
        """Embed content for STPM with fast retrieval embeddings."""

        stpm_metadata = {"project_id": project_id}
        if metadata:
            stpm_metadata.update(metadata)

        return await self.vector_db.add_document(
            document_id=content_id,
            content=content,
            namespace=EmbeddingNamespace.STPM,
            metadata=stpm_metadata,
            embedding_type="stpm"
        )

    async def search_ltkb(
        self,
        query: str,
        limit: int = 10,
        filters: Optional[Dict[str, Any]] = None
    ) -> List[SearchResult]:
        """Search LTKB with long-context embeddings."""

        return await self.vector_db.search_similar(
            query=query,
            namespace=EmbeddingNamespace.LTKB,
            limit=limit,
            embedding_type="ltkb",
            filters=filters
        )

    async def search_stpm(
        self,
        query: str,
        project_id: Optional[str] = None,
        limit: int = 10,
        filters: Optional[Dict[str, Any]] = None
    ) -> List[SearchResult]:
        """Search STPM with fast retrieval embeddings."""

        search_filters = filters or {}
        if project_id:
            search_filters["project_id"] = project_id

        return await self.vector_db.search_similar(
            query=query,
            namespace=EmbeddingNamespace.STPM,
            limit=limit,
            embedding_type="stpm",
            filters=search_filters
        )

    async def get_relevant_context(
        self,
        query: str,
        project_id: Optional[str] = None,
        include_ltkb: bool = True,
        include_stpm: bool = True,
        limit_per_source: int = 5
    ) -> Tuple[str, str]:
        """Get relevant context from both LTKB and STPM."""

        ltkb_context = ""
        stpm_context = ""

        if include_ltkb:
            ltkb_results = await self.search_ltkb(query, limit=limit_per_source)
            ltkb_chunks = [result.chunk.content for result in ltkb_results]
            ltkb_context = "\n\n".join(ltkb_chunks)

        if include_stpm:
            stpm_results = await self.search_stpm(query, project_id=project_id, limit=limit_per_source)
            stpm_chunks = [result.chunk.content for result in stpm_results]
            stpm_context = "\n\n".join(stpm_chunks)

        return ltkb_context, stpm_context

    async def close(self):
        """Close the vector database connection."""
        await self.vector_db.close()


# Global instances
vector_db = None
embedding_agent = None

def get_vector_db() -> VectorDBClient:
    """Get global vector database instance."""
    global vector_db
    if vector_db is None:
        vector_db = VectorDBClient()
    return vector_db

def get_embedding_agent() -> EmbeddingAgent:
    """Get global embedding agent instance."""
    global embedding_agent
    if embedding_agent is None:
        embedding_agent = EmbeddingAgent(get_vector_db())
    return embedding_agent
