"""
Ollama AI Provider

Implementation of the AI provider interface for Ollama local models.
Provides intelligent agent-aware model routing and specialized capabilities.
"""

import json
import time
from typing import Dict, List, Optional, AsyncGenerator, Any
import aiohttp
import asyncio
from datetime import datetime

from ....agents import AgentRole, AGENT_CONFIGS
from ....config import settings
from ..base import (
    AIProvider,
    ProviderType,
    ChatRequest,
    ChatResponse,
    ChatMessage,
    ModelInfo,
    ModelHealthCheck,
    ProviderHealthCheck,
    HealthStatus,
)


class OllamaProvider(AIProvider):
    """Ollama local AI provider with agent-aware model routing."""

    def __init__(self):
        super().__init__("ollama", ProviderType.LOCAL)
        self.base_url = settings.ai.ollama_host.rstrip("/")
        self.session: Optional[aiohttp.ClientSession] = None

    async def _get_session(self) -> aiohttp.ClientSession:
        """Get or create HTTP session."""
        if self.session is None or self.session.closed:
            self.session = aiohttp.ClientSession(
                timeout=aiohttp.ClientTimeout(total=300)  # 5 minutes for large model responses
            )
        return self.session

    async def close(self):
        """Close the HTTP session."""
        if self.session and not self.session.closed:
            await self.session.close()

    async def chat(self, request: ChatRequest) -> ChatResponse:
        """Generate a chat completion response using Ollama."""
        try:
            # Determine the best model for this request
            model = await self._select_model(request)

            # Convert messages to Ollama format
            ollama_messages = [
                {"role": msg.role, "content": msg.content}
                for msg in request.messages
            ]

            # Add system prompt if agent role is specified
            if request.agent_role:
                agent_config = AGENT_CONFIGS.get(request.agent_role)
                if agent_config and ollama_messages:
                    # Insert system message at the beginning
                    ollama_messages.insert(0, {
                        "role": "system",
                        "content": agent_config.system_prompt
                    })

            # Prepare request payload
            payload = {
                "model": model,
                "messages": ollama_messages,
                "stream": False,
                "options": self._get_model_options(request, model)
            }

            session = await self._get_session()
            start_time = time.time()

            async with session.post(f"{self.base_url}/api/chat", json=payload) as response:
                if response.status != 200:
                    error_text = await response.text()
                    raise Exception(f"Ollama API error {response.status}: {error_text}")

                result = await response.json()
                response_time = time.time() - start_time

                return ChatResponse(
                    content=result.get("message", {}).get("content", ""),
                    model=model,
                    agent_role=request.agent_role,
                    tokens_used=result.get("eval_count"),
                    finish_reason=result.get("done_reason"),
                    metadata={
                        "response_time_ms": round(response_time * 1000, 2),
                        "prompt_eval_count": result.get("prompt_eval_count"),
                        "eval_count": result.get("eval_count"),
                        "eval_duration": result.get("eval_duration"),
                    }
                )

        except Exception as e:
            raise Exception(f"Chat completion failed: {str(e)}")

    async def stream_chat(self, request: ChatRequest) -> AsyncGenerator[str, None]:
        """Generate a streaming chat completion response."""
        try:
            model = await self._select_model(request)

            # Convert messages to Ollama format
            ollama_messages = [
                {"role": msg.role, "content": msg.content}
                for msg in request.messages
            ]

            # Add system prompt if agent role is specified
            if request.agent_role:
                agent_config = AGENT_CONFIGS.get(request.agent_role)
                if agent_config and ollama_messages:
                    ollama_messages.insert(0, {
                        "role": "system",
                        "content": agent_config.system_prompt
                    })

            payload = {
                "model": model,
                "messages": ollama_messages,
                "stream": True,
                "options": self._get_model_options(request, model)
            }

            session = await self._get_session()

            async with session.post(f"{self.base_url}/api/chat", json=payload) as response:
                if response.status != 200:
                    error_text = await response.text()
                    raise Exception(f"Ollama API error {response.status}: {error_text}")

                async for line in response.content:
                    if line:
                        try:
                            chunk = json.loads(line.decode().strip())
                            if "message" in chunk and "content" in chunk["message"]:
                                content = chunk["message"]["content"]
                                if content:
                                    yield content
                        except json.JSONDecodeError:
                            continue

        except Exception as e:
            raise Exception(f"Streaming chat failed: {str(e)}")

    async def _select_model(self, request: ChatRequest) -> str:
        """Select the best model for the request based on agent role."""
        # If model is explicitly specified, use it
        if request.model:
            return request.model

        # If agent role is specified, use the configured model for that agent
        if request.agent_role:
            agent_config = AGENT_CONFIGS.get(request.agent_role)
            if agent_config:
                return agent_config.model

        # Default fallback model
        return settings.ai.default_model

    def _get_model_options(self, request: ChatRequest, model: str) -> Dict[str, Any]:
        """Get model-specific options for the request."""
        options = {}

        # Set temperature
        if request.temperature is not None:
            options["temperature"] = request.temperature
        elif request.agent_role:
            agent_config = AGENT_CONFIGS.get(request.agent_role)
            if agent_config:
                options["temperature"] = agent_config.temperature

        # Set max tokens
        if request.max_tokens is not None:
            options["num_predict"] = request.max_tokens
        elif request.agent_role:
            agent_config = AGENT_CONFIGS.get(request.agent_role)
            if agent_config:
                options["num_predict"] = agent_config.max_tokens

        # Model-specific optimizations
        if "yi-coder" in model:
            # Optimize for fast code completion
            options.update({
                "top_k": 40,
                "top_p": 0.9,
                "repeat_penalty": 1.1,
            })
        elif "starcoder" in model:
            # Optimize for code generation
            options.update({
                "top_k": 50,
                "top_p": 0.95,
                "repeat_penalty": 1.05,
            })
        elif "deepseek-coder" in model:
            # Optimize for complex analysis
            options.update({
                "top_k": 60,
                "top_p": 0.8,
                "repeat_penalty": 1.1,
            })

        return options

    async def health_check(self) -> ProviderHealthCheck:
        """Check the health of Ollama and its models."""
        try:
            session = await self._get_session()

            # Check if Ollama is running
            try:
                async with session.get(f"{self.base_url}/api/tags") as response:
                    if response.status != 200:
                        return ProviderHealthCheck(
                            provider_name=self.name,
                            provider_type=self.provider_type,
                            status=HealthStatus.UNHEALTHY,
                            error=f"Ollama API returned status {response.status}"
                        )

                    models_data = await response.json()

            except Exception as e:
                return ProviderHealthCheck(
                    provider_name=self.name,
                    provider_type=self.provider_type,
                    status=HealthStatus.UNHEALTHY,
                    error=f"Failed to connect to Ollama: {str(e)}"
                )

            # Check health of individual models
            model_checks = []
            agent_models = {config.model for config in AGENT_CONFIGS.values()}

            for model_name in agent_models:
                model_check = await self._check_model_health(model_name)
                model_checks.append(model_check)

            # Determine overall status
            healthy_models = sum(1 for check in model_checks if check.status == HealthStatus.HEALTHY)
            total_models = len(model_checks)

            if healthy_models == total_models:
                overall_status = HealthStatus.HEALTHY
            elif healthy_models > 0:
                overall_status = HealthStatus.DEGRADED
            else:
                overall_status = HealthStatus.UNHEALTHY

            return ProviderHealthCheck(
                provider_name=self.name,
                provider_type=self.provider_type,
                status=overall_status,
                models=model_checks
            )

        except Exception as e:
            return ProviderHealthCheck(
                provider_name=self.name,
                provider_type=self.provider_type,
                status=HealthStatus.UNHEALTHY,
                error=f"Health check failed: {str(e)}"
            )

    async def _check_model_health(self, model_name: str) -> ModelHealthCheck:
        """Check the health of a specific model."""
        try:
            start_time = time.time()

            # Simple test query
            test_payload = {
                "model": model_name,
                "prompt": "Hello",
                "stream": False,
                "options": {"num_predict": 1}
            }

            session = await self._get_session()
            async with session.post(f"{self.base_url}/api/generate", json=test_payload) as response:
                if response.status == 200:
                    await response.json()  # Consume response
                    latency = (time.time() - start_time) * 1000
                    return ModelHealthCheck(
                        model=model_name,
                        status=HealthStatus.HEALTHY,
                        latency_ms=round(latency, 2)
                    )
                else:
                    error_text = await response.text()
                    return ModelHealthCheck(
                        model=model_name,
                        status=HealthStatus.UNHEALTHY,
                        error=f"HTTP {response.status}: {error_text}"
                    )

        except Exception as e:
            return ModelHealthCheck(
                model=model_name,
                status=HealthStatus.UNHEALTHY,
                error=str(e)
            )

    async def get_models(self) -> List[ModelInfo]:
        """Get information about available models."""
        try:
            session = await self._get_session()
            async with session.get(f"{self.base_url}/api/tags") as response:
                if response.status != 200:
                    return []

                data = await response.json()
                models = []

                for model_data in data.get("models", []):
                    model_name = model_data.get("name", "")

                    # Find which agent roles use this model
                    agent_roles = [
                        role for role, config in AGENT_CONFIGS.items()
                        if config.model == model_name
                    ]

                    # Determine capabilities based on model name
                    capabilities = self._get_model_capabilities(model_name)

                    models.append(ModelInfo(
                        name=model_name,
                        size=model_data.get("size"),
                        capabilities=capabilities,
                        agent_roles=agent_roles,
                        context_window=self._get_model_context_window(model_name)
                    ))

                return models

        except Exception as e:
            raise Exception(f"Failed to get models: {str(e)}")

    def _get_model_capabilities(self, model_name: str) -> List[str]:
        """Get capabilities for a model based on its name."""
        capabilities = []

        if "yi-coder" in model_name:
            capabilities.extend(["fast_completion", "code_generation", "syntax_highlighting"])
        elif "mistral" in model_name:
            capabilities.extend(["chat", "explanations", "documentation", "reasoning"])
        elif "qwen" in model_name:
            capabilities.extend(["code_analysis", "optimization", "multilingual"])
        elif "starcoder" in model_name:
            capabilities.extend(["advanced_coding", "refactoring", "code_transformation"])
        elif "deepseek-coder" in model_name:
            capabilities.extend(["complex_algorithms", "architecture_design", "debugging"])

        return capabilities

    def _get_model_context_window(self, model_name: str) -> Optional[int]:
        """Get context window size for a model."""
        # These are approximate values based on model specifications
        if "yi-coder:1.5b" in model_name:
            return 4096
        elif "mistral:7b" in model_name:
            return 8192
        elif "qwen2.5:3b" in model_name:
            return 32768
        elif "starcoder2:3b" in model_name:
            return 16384
        elif "deepseek-coder:6.7b" in model_name:
            return 16384
        else:
            return None

    async def get_model_for_agent(self, agent_role: AgentRole) -> Optional[str]:
        """Get the recommended model for a specific agent role."""
        agent_config = AGENT_CONFIGS.get(agent_role)
        return agent_config.model if agent_config else None
