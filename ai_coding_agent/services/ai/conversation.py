"""
Conversation Manager

Handles agent-specific conversation contexts, memory, and multi-turn interactions.
"""

import uuid
from typing import Dict, List, Optional, Any
from datetime import datetime, timedelta

from pydantic import BaseModel, Field

from ...agents import AgentRole
from .base import ChatMessage


class ConversationContext(BaseModel):
    """Context for an ongoing conversation."""
    conversation_id: str
    agent_role: Optional[AgentRole] = None
    user_id: Optional[str] = None
    project_context: Optional[Dict[str, Any]] = None
    created_at: datetime = Field(default_factory=datetime.now)
    last_activity: datetime = Field(default_factory=datetime.now)
    metadata: Dict[str, Any] = Field(default_factory=dict)


class Conversation(BaseModel):
    """A conversation with message history and context."""
    context: ConversationContext
    messages: List[ChatMessage] = Field(default_factory=list)

    def add_message(self, role: str, content: str) -> None:
        """Add a message to the conversation."""
        message = ChatMessage(role=role, content=content)
        self.messages.append(message)
        self.context.last_activity = datetime.now()

    def get_recent_messages(self, limit: int = 10) -> List[ChatMessage]:
        """Get the most recent messages."""
        return self.messages[-limit:]


class ConversationManager:
    """Manages multiple agent conversations with context and memory."""

    def __init__(self, max_conversations: int = 1000, conversation_ttl_hours: int = 24):
        self.conversations: Dict[str, Conversation] = {}
        self.max_conversations = max_conversations
        self.conversation_ttl = timedelta(hours=conversation_ttl_hours)

    async def create_conversation(
        self,
        agent_role: Optional[AgentRole] = None,
        user_id: Optional[str] = None,
        project_context: Optional[Dict[str, Any]] = None
    ) -> str:
        """
        Create a new conversation.

        Args:
            agent_role: The agent role for this conversation
            user_id: The user ID (optional)
            project_context: Project-specific context (optional)

        Returns:
            The conversation ID
        """
        conversation_id = str(uuid.uuid4())

        context = ConversationContext(
            conversation_id=conversation_id,
            agent_role=agent_role,
            user_id=user_id,
            project_context=project_context or {}
        )

        conversation = Conversation(context=context)
        self.conversations[conversation_id] = conversation

        # Clean up old conversations if we exceed the limit
        await self._cleanup_conversations()

        return conversation_id

    async def get_conversation(self, conversation_id: str) -> Optional[Conversation]:
        """Get a conversation by ID."""
        conversation = self.conversations.get(conversation_id)

        if conversation:
            # Check if conversation has expired
            if datetime.now() - conversation.context.last_activity > self.conversation_ttl:
                await self.delete_conversation(conversation_id)
                return None

        return conversation

    async def add_message(
        self,
        conversation_id: str,
        role: str,
        content: str
    ) -> bool:
        """
        Add a message to a conversation.

        Args:
            conversation_id: The conversation ID
            role: The message role ("user", "assistant", "system")
            content: The message content

        Returns:
            True if successful, False if conversation not found
        """
        conversation = await self.get_conversation(conversation_id)
        if not conversation:
            return False

        conversation.add_message(role, content)
        return True

    async def get_conversation_messages(
        self,
        conversation_id: str,
        limit: Optional[int] = None
    ) -> List[ChatMessage]:
        """
        Get messages from a conversation.

        Args:
            conversation_id: The conversation ID
            limit: Maximum number of recent messages to return

        Returns:
            List of messages, or empty list if conversation not found
        """
        conversation = await self.get_conversation(conversation_id)
        if not conversation:
            return []

        if limit:
            return conversation.get_recent_messages(limit)
        else:
            return conversation.messages

    async def update_project_context(
        self,
        conversation_id: str,
        context_updates: Dict[str, Any]
    ) -> bool:
        """
        Update project context for a conversation.

        Args:
            conversation_id: The conversation ID
            context_updates: Dictionary of context updates

        Returns:
            True if successful, False if conversation not found
        """
        conversation = await self.get_conversation(conversation_id)
        if not conversation:
            return False

        if not conversation.context.project_context:
            conversation.context.project_context = {}

        conversation.context.project_context.update(context_updates)
        conversation.context.last_activity = datetime.now()
        return True

    async def delete_conversation(self, conversation_id: str) -> bool:
        """
        Delete a conversation.

        Args:
            conversation_id: The conversation ID

        Returns:
            True if deleted, False if not found
        """
        if conversation_id in self.conversations:
            del self.conversations[conversation_id]
            return True
        return False

    async def get_user_conversations(
        self,
        user_id: str,
        agent_role: Optional[AgentRole] = None
    ) -> List[ConversationContext]:
        """
        Get all conversations for a user.

        Args:
            user_id: The user ID
            agent_role: Filter by agent role (optional)

        Returns:
            List of conversation contexts
        """
        conversations = []

        for conversation in self.conversations.values():
            if conversation.context.user_id == user_id:
                if agent_role is None or conversation.context.agent_role == agent_role:
                    conversations.append(conversation.context)

        # Sort by last activity (most recent first)
        conversations.sort(key=lambda c: c.last_activity, reverse=True)
        return conversations

    async def get_agent_conversations(
        self,
        agent_role: AgentRole,
        user_id: Optional[str] = None
    ) -> List[ConversationContext]:
        """
        Get all conversations for a specific agent.

        Args:
            agent_role: The agent role
            user_id: Filter by user ID (optional)

        Returns:
            List of conversation contexts
        """
        conversations = []

        for conversation in self.conversations.values():
            if conversation.context.agent_role == agent_role:
                if user_id is None or conversation.context.user_id == user_id:
                    conversations.append(conversation.context)

        conversations.sort(key=lambda c: c.last_activity, reverse=True)
        return conversations

    async def _cleanup_conversations(self) -> None:
        """Clean up old conversations if we exceed limits."""
        # Remove expired conversations
        now = datetime.now()
        expired_ids = [
            conv_id for conv_id, conv in self.conversations.items()
            if now - conv.context.last_activity > self.conversation_ttl
        ]

        for conv_id in expired_ids:
            del self.conversations[conv_id]

        # If still over limit, remove oldest conversations
        if len(self.conversations) > self.max_conversations:
            # Sort by last activity and keep only the most recent
            sorted_conversations = sorted(
                self.conversations.items(),
                key=lambda x: x[1].context.last_activity,
                reverse=True
            )

            # Keep only the most recent conversations
            self.conversations = dict(sorted_conversations[:self.max_conversations])

    async def get_conversation_stats(self) -> Dict[str, Any]:
        """Get statistics about active conversations."""
        now = datetime.now()
        active_conversations = len(self.conversations)

        # Count by agent role
        agent_counts = {}
        for conv in self.conversations.values():
            role = conv.context.agent_role
            if role:
                agent_counts[role.value] = agent_counts.get(role.value, 0) + 1

        # Count by recency
        recent_24h = sum(
            1 for conv in self.conversations.values()
            if now - conv.context.last_activity < timedelta(hours=24)
        )

        recent_1h = sum(
            1 for conv in self.conversations.values()
            if now - conv.context.last_activity < timedelta(hours=1)
        )

        return {
            "total_conversations": active_conversations,
            "conversations_by_agent": agent_counts,
            "recent_24h": recent_24h,
            "recent_1h": recent_1h,
            "max_conversations": self.max_conversations,
            "conversation_ttl_hours": self.conversation_ttl.total_seconds() / 3600
        }
