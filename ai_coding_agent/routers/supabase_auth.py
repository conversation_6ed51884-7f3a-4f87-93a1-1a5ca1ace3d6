"""
Enhanced Authentication router with Supabase Auth integration.

This module provides REST API endpoints for user authentication using
Supabase Auth while maintaining local user profile management.
"""

from datetime import <PERSON><PERSON><PERSON>
from typing import Any, Dict

from fastapi import APIRouter, Depends, HTTPException, status, Request
from fastapi.security import <PERSON><PERSON><PERSON><PERSON>ear<PERSON>
from sqlalchemy.orm import Session

from ..config import settings
from ..models import get_db, User, UserCreate, UserResponse, UserLogin
from ..services.supabase_auth import get_supabase_auth_service, SupabaseAuthService
from ..services import auth as legacy_auth

router = APIRouter()
security = HTTPBearer()


@router.post("/register", status_code=status.HTTP_201_CREATED)
async def register_user(
    user_data: UserCreate,
    supabase_auth: SupabaseAuthService = Depends(get_supabase_auth_service)
) -> Dict[str, Any]:
    """
    Register a new user account using Supabase Auth.

    Creates a new user with Supabase authentication and syncs to local database.

    Args:
        user_data: User registration data
        supabase_auth: Supabase authentication service

    Returns:
        Dict containing user info, tokens, and confirmation message

    Raises:
        HTTPException: If registration fails
    """
    try:
        # Additional user metadata
        user_metadata = {
            "username": user_data.username,
            "full_name": user_data.full_name or ""
        }

        result = await supabase_auth.register_user(
            email=user_data.email,
            password=user_data.password,
            user_data=user_metadata
        )

        return result

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Registration failed: {str(e)}"
        )


@router.post("/login")
async def login_user(
    login_data: UserLogin,
    supabase_auth: SupabaseAuthService = Depends(get_supabase_auth_service)
) -> Dict[str, Any]:
    """
    Authenticate user using Supabase Auth.

    Validates credentials against Supabase and returns authentication tokens.

    Args:
        login_data: User login credentials
        supabase_auth: Supabase authentication service

    Returns:
        Dict containing user info and authentication tokens

    Raises:
        HTTPException: If authentication fails
    """
    try:
        result = await supabase_auth.login_user(
            email=login_data.username,  # UserLogin uses username field for email
            password=login_data.password
        )

        return result

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Login failed: {str(e)}"
        )


@router.post("/logout")
async def logout_user(
    request: Request,
    supabase_auth: SupabaseAuthService = Depends(get_supabase_auth_service)
) -> Dict[str, str]:
    """
    Logout user and invalidate session.

    Args:
        request: FastAPI request object
        supabase_auth: Supabase authentication service

    Returns:
        Dict with logout confirmation
    """
    try:
        # Extract token from Authorization header
        authorization = request.headers.get("Authorization")
        if not authorization or not authorization.startswith("Bearer "):
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Authorization header missing"
            )

        access_token = authorization.split(" ")[1]
        result = await supabase_auth.logout_user(access_token)

        return result

    except HTTPException:
        raise
    except Exception as e:
        return {"message": "Logout completed"}


@router.post("/refresh")
async def refresh_tokens(
    refresh_token_data: Dict[str, str],
    supabase_auth: SupabaseAuthService = Depends(get_supabase_auth_service)
) -> Dict[str, Any]:
    """
    Refresh authentication tokens.

    Args:
        refresh_token_data: Dict containing refresh_token
        supabase_auth: Supabase authentication service

    Returns:
        Dict with new authentication tokens
    """
    try:
        refresh_token = refresh_token_data.get("refresh_token")
        if not refresh_token:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Refresh token required"
            )

        result = await supabase_auth.refresh_token(refresh_token)
        return result

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Token refresh failed: {str(e)}"
        )


@router.post("/reset-password")
async def reset_password(
    email_data: Dict[str, str],
    supabase_auth: SupabaseAuthService = Depends(get_supabase_auth_service)
) -> Dict[str, str]:
    """
    Send password reset email.

    Args:
        email_data: Dict containing email address
        supabase_auth: Supabase authentication service

    Returns:
        Dict with confirmation message
    """
    try:
        email = email_data.get("email")
        if not email:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Email address required"
            )

        result = await supabase_auth.reset_password(email)
        return result

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Password reset failed: {str(e)}"
        )


@router.get("/me")
async def get_current_user(
    request: Request,
    supabase_auth: SupabaseAuthService = Depends(get_supabase_auth_service),
    db: Session = Depends(get_db)
) -> Dict[str, Any]:
    """
    Get current authenticated user information.

    Args:
        request: FastAPI request object
        supabase_auth: Supabase authentication service
        db: Database session

    Returns:
        Dict with current user information
    """
    try:
        # Extract token from Authorization header
        authorization = request.headers.get("Authorization")
        if not authorization or not authorization.startswith("Bearer "):
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Authorization header missing"
            )

        access_token = authorization.split(" ")[1]

        # Get user from Supabase
        supabase_user = await supabase_auth.get_user_by_token(access_token)
        if not supabase_user:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid or expired token"
            )

        # Get local user profile
        local_user = db.query(User).filter(User.supabase_id == supabase_user.id).first()

        response = {
            "supabase_user": {
                "id": supabase_user.id,
                "email": supabase_user.email,
                "email_confirmed_at": supabase_user.email_confirmed_at,
                "user_metadata": supabase_user.user_metadata or {},
                "created_at": supabase_user.created_at,
                "updated_at": supabase_user.updated_at
            }
        }

        if local_user:
            response["local_user"] = {
                "id": local_user.id,
                "username": local_user.username,
                "full_name": local_user.full_name,
                "is_active": local_user.is_active,
                "created_at": local_user.created_at.isoformat(),
                "updated_at": local_user.updated_at.isoformat() if local_user.updated_at is not None else None
            }

        return response

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get user info: {str(e)}"
        )


@router.put("/me")
async def update_current_user(
    user_update_data: Dict[str, Any],
    request: Request,
    supabase_auth: SupabaseAuthService = Depends(get_supabase_auth_service)
) -> Dict[str, Any]:
    """
    Update current user's profile information.

    Args:
        user_update_data: User data to update
        request: FastAPI request object
        supabase_auth: Supabase authentication service

    Returns:
        Dict with updated user information
    """
    try:
        # Extract token from Authorization header
        authorization = request.headers.get("Authorization")
        if not authorization or not authorization.startswith("Bearer "):
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Authorization header missing"
            )

        access_token = authorization.split(" ")[1]

        # Filter user metadata (only allow safe fields)
        allowed_fields = {"username", "full_name", "avatar_url", "bio"}
        user_metadata = {
            key: value for key, value in user_update_data.items()
            if key in allowed_fields
        }

        result = await supabase_auth.update_user_metadata(access_token, user_metadata)
        return result

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to update user: {str(e)}"
        )


# Legacy endpoints for backward compatibility
@router.post("/legacy/register", response_model=UserResponse, status_code=status.HTTP_201_CREATED)
async def legacy_register_user(
    user_data: UserCreate,
    db: Session = Depends(get_db)
) -> Any:
    """
    Legacy user registration endpoint (local database only).

    This endpoint is kept for backward compatibility and testing.
    New applications should use the /register endpoint with Supabase.
    """
    try:
        from ..services import user as user_service
        db_user = user_service.create_user(db=db, user=user_data)
        return UserResponse.model_validate(db_user)
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Registration failed: {str(e)}"
        )


@router.post("/legacy/login")
async def legacy_login_user(
    login_data: UserLogin,
    db: Session = Depends(get_db)
) -> Dict[str, Any]:
    """
    Legacy user login endpoint (local database only).

    This endpoint is kept for backward compatibility and testing.
    New applications should use the /login endpoint with Supabase.
    """
    try:
        from ..services import user as user_service

        # For legacy compatibility, assume username field contains email
        user = db.query(User).filter(User.email == login_data.username).first()
        if not user:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid email or password"
            )

        # Check if user has a hashed password and verify it
        hashed_pw = getattr(user, 'hashed_password', None)
        if not hashed_pw or not legacy_auth.verify_password(login_data.password, hashed_pw):
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid email or password"
            )

        # Create access token
        access_token = legacy_auth.create_access_token(
            data={"sub": user.email},
            expires_delta=timedelta(minutes=settings.security.access_token_expire_minutes)
        )

        return {
            "access_token": access_token,
            "token_type": "bearer",
            "user": UserResponse.model_validate(user)
        }

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Login failed: {str(e)}"
        )
