"""
User model and schemas for authentication and user management.

This module contains the User SQLAlchemy model and Pydantic schemas
for user operations.
"""

from datetime import datetime
from typing import Optional

from sqlalchemy import <PERSON>olean, Column, DateTime, Integer, String
from sqlalchemy.sql import func
from pydantic import BaseModel, EmailStr, Field, ConfigDict

from .base import Base


class User(Base):
    """
    User SQLAlchemy model for database storage.

    Stores user authentication and profile information with
    security-first approach.
    """

    __tablename__ = "users"

    id = Column(Integer, primary_key=True, index=True)
    supabase_id = Column(String, unique=True, index=True, nullable=True)  # Supabase Auth user ID
    email = Column(String, unique=True, index=True, nullable=False)
    username = Column(String, unique=True, index=True, nullable=False)
    full_name = Column(String, nullable=True)
    hashed_password = Column(String, nullable=True)  # Optional for Supabase users
    is_active = Column(Boolean, default=True)
    is_superuser = Column(Boolean, default=False)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())


# Pydantic schemas for API serialization
class UserBase(BaseModel):
    """Base user schema with common fields."""

    email: EmailStr
    username: str = Field(..., min_length=3, max_length=50, pattern="^[a-zA-Z0-9_-]+$")
    full_name: Optional[str] = Field(None, max_length=100)
    is_active: bool = True


class UserCreate(UserBase):
    """Schema for user creation with password validation."""

    password: str = Field(..., min_length=8, max_length=100)

    model_config = ConfigDict(
        json_schema_extra={
            "example": {
                "email": "<EMAIL>",
                "username": "johndoe",
                "full_name": "John Doe",
                "password": "secure_password123"
            }
        }
    )


class UserUpdate(BaseModel):
    """Schema for user updates with optional fields."""

    email: Optional[EmailStr] = None
    username: Optional[str] = Field(None, min_length=3, max_length=50, pattern="^[a-zA-Z0-9_-]+$")
    full_name: Optional[str] = Field(None, max_length=100)
    password: Optional[str] = Field(None, min_length=8, max_length=100)
    is_active: Optional[bool] = None


class UserResponse(UserBase):
    """Schema for user responses (excludes sensitive data)."""

    id: int
    is_superuser: bool
    created_at: datetime
    updated_at: Optional[datetime]

    model_config = ConfigDict(from_attributes=True)


class UserLogin(BaseModel):
    """Schema for user login credentials."""

    username: str = Field(..., description="Username or email")
    password: str = Field(..., description="User password")

    model_config = ConfigDict(
        json_schema_extra={
            "example": {
                "username": "johndoe",
                "password": "secure_password123"
            }
        }
    )
