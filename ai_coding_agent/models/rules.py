"""
Rules Engine Models - Phase B2 Implementation
Implements the rules system for agent behavior governance.
"""

from datetime import datetime
from typing import Dict, List, Optional, Literal, Any
from enum import Enum
from uuid import uuid4

from sqlalchemy import Column, String, Text, DateTime, ForeignKey, JSON, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>
from sqlalchemy.orm import relationship, Mapped, mapped_column
from sqlalchemy.dialects.postgresql import UUID
from pydantic import BaseModel, Field

from .base import Base


class RuleType(str, Enum):
    """Types of rules in the system."""
    GLOBAL = "global"           # Apply to all projects
    PROJECT = "project"         # Apply to specific project
    AGENT = "agent"            # Apply to specific agent type
    TECH_STACK = "tech_stack"   # Apply to specific technology


class RuleSeverity(str, Enum):
    """Rule violation severity levels."""
    CRITICAL = "critical"       # Block execution
    ERROR = "error"            # Log and continue with caution
    WARNING = "warning"        # Log but continue normally
    INFO = "info"              # Informational only


class RuleCategory(str, Enum):
    """Categories of rules."""
    SECURITY = "security"
    CODE_QUALITY = "code_quality"
    NAMING = "naming"
    STRUCTURE = "structure"
    DEPENDENCIES = "dependencies"
    TESTING = "testing"
    DOCUMENTATION = "documentation"
    PERFORMANCE = "performance"


# SQLAlchemy Models

class Rule(Base):
    """Rule definition for agent behavior governance."""
    __tablename__ = "rules"

    id: Mapped[str] = mapped_column(UUID(as_uuid=False), primary_key=True, default=lambda: str(uuid4()))
    name: Mapped[str] = mapped_column(String(255), nullable=False)
    description: Mapped[str] = mapped_column(Text, nullable=False)
    rule_type: Mapped[str] = mapped_column(String(50), nullable=False)
    category: Mapped[str] = mapped_column(String(50), nullable=False)
    severity: Mapped[str] = mapped_column(String(50), nullable=False, default=RuleSeverity.WARNING)

    # Rule conditions and actions
    conditions: Mapped[Dict] = mapped_column(JSON, nullable=False)
    actions: Mapped[Dict] = mapped_column(JSON, nullable=False)

    # Scope constraints
    project_id: Mapped[Optional[str]] = mapped_column(UUID(as_uuid=False), ForeignKey("projects.id"), nullable=True)
    agent_types: Mapped[List[str]] = mapped_column(JSON, nullable=False, default=list)
    tech_stack: Mapped[List[str]] = mapped_column(JSON, nullable=False, default=list)

    # Rule metadata
    is_active: Mapped[bool] = mapped_column(Boolean, nullable=False, default=True)
    priority: Mapped[int] = mapped_column(Integer, nullable=False, default=100)
    created_by: Mapped[str] = mapped_column(String(255), nullable=False, default="system")
    rule_metadata: Mapped[Optional[Dict]] = mapped_column(JSON, nullable=True)

    created_at: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow)
    updated_at: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Relationships
    violations: Mapped[List["RuleViolation"]] = relationship("RuleViolation", back_populates="rule")


class RuleViolation(Base):
    """Track rule violations and agent responses."""
    __tablename__ = "rule_violations"

    id: Mapped[str] = mapped_column(UUID(as_uuid=False), primary_key=True, default=lambda: str(uuid4()))
    rule_id: Mapped[str] = mapped_column(UUID(as_uuid=False), ForeignKey("rules.id"), nullable=False)
    project_id: Mapped[Optional[str]] = mapped_column(UUID(as_uuid=False), ForeignKey("projects.id"), nullable=True)
    task_id: Mapped[Optional[str]] = mapped_column(UUID(as_uuid=False), ForeignKey("tasks.id"), nullable=True)

    # Violation details
    agent_type: Mapped[str] = mapped_column(String(50), nullable=False)
    violation_context: Mapped[Dict] = mapped_column(JSON, nullable=False)
    attempted_action: Mapped[str] = mapped_column(Text, nullable=False)

    # Response details
    was_blocked: Mapped[bool] = mapped_column(Boolean, nullable=False, default=False)
    agent_response: Mapped[Optional[str]] = mapped_column(Text, nullable=True)
    resolution_status: Mapped[str] = mapped_column(String(50), nullable=False, default="unresolved")

    created_at: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow)

    # Relationships
    rule: Mapped["Rule"] = relationship("Rule", back_populates="violations")


# Pydantic Schemas

class RuleCondition(BaseModel):
    """Rule condition definition."""
    field: str = Field(..., description="Field to check (e.g., 'file_extension', 'function_name')")
    operator: Literal["equals", "contains", "starts_with", "ends_with", "regex", "not_equals"] = Field(..., description="Comparison operator")
    value: Any = Field(..., description="Value to compare against")
    case_sensitive: bool = Field(default=True, description="Whether comparison is case sensitive")


class RuleAction(BaseModel):
    """Rule action definition."""
    type: Literal["block", "warn", "log", "modify", "suggest"] = Field(..., description="Action type")
    message: str = Field(..., description="Message to display/log")
    auto_fix: Optional[str] = Field(None, description="Automatic fix to apply")
    suggestions: List[str] = Field(default_factory=list, description="Suggested alternatives")


class RuleBase(BaseModel):
    """Base rule schema."""
    name: str = Field(..., description="Rule name")
    description: str = Field(..., description="Rule description")
    rule_type: RuleType
    category: RuleCategory
    severity: RuleSeverity = RuleSeverity.WARNING
    conditions: List[RuleCondition] = Field(..., description="Rule conditions")
    actions: RuleAction = Field(..., description="Rule actions")
    agent_types: List[str] = Field(default_factory=list, description="Applicable agent types")
    tech_stack: List[str] = Field(default_factory=list, description="Applicable tech stack")
    is_active: bool = True
    priority: int = Field(default=100, ge=1, le=1000, description="Rule priority (1=highest)")


class RuleCreate(RuleBase):
    """Schema for creating rules."""
    project_id: Optional[str] = None
    created_by: str = Field(default="user", description="Rule creator")


class RuleUpdate(BaseModel):
    """Schema for updating rules."""
    name: Optional[str] = None
    description: Optional[str] = None
    severity: Optional[RuleSeverity] = None
    conditions: Optional[List[RuleCondition]] = None
    actions: Optional[RuleAction] = None
    is_active: Optional[bool] = None
    priority: Optional[int] = None


class RuleResponse(RuleBase):
    """Schema for rule responses."""
    id: str
    project_id: Optional[str] = None
    created_by: str
    rule_metadata: Optional[Dict] = None
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


class RuleViolationBase(BaseModel):
    """Base rule violation schema."""
    agent_type: str
    violation_context: Dict
    attempted_action: str
    was_blocked: bool = False
    agent_response: Optional[str] = None


class RuleViolationCreate(RuleViolationBase):
    """Schema for creating rule violations."""
    rule_id: str
    project_id: Optional[str] = None
    task_id: Optional[str] = None


class RuleViolationResponse(RuleViolationBase):
    """Schema for rule violation responses."""
    id: str
    rule_id: str
    project_id: Optional[str] = None
    task_id: Optional[str] = None
    resolution_status: str
    created_at: datetime

    class Config:
        from_attributes = True


# Predefined Global Rules

GLOBAL_RULES = [
    {
        "name": "No Hardcoded Secrets",
        "description": "Prevent hardcoded API keys, passwords, and secrets in code",
        "rule_type": RuleType.GLOBAL,
        "category": RuleCategory.SECURITY,
        "severity": RuleSeverity.CRITICAL,
        "conditions": [
            {
                "field": "file_content",
                "operator": "regex",
                "value": r"(api_key|password|secret|token)\s*=\s*['\"][^'\"]+['\"]",
                "case_sensitive": False
            }
        ],
        "actions": {
            "type": "block",
            "message": "Hardcoded secrets detected. Use environment variables instead.",
            "suggestions": ["Use os.getenv()", "Use environment variables", "Use config files"]
        }
    },
    {
        "name": "Function Name Convention",
        "description": "Enforce snake_case for function names",
        "rule_type": RuleType.GLOBAL,
        "category": RuleCategory.NAMING,
        "severity": RuleSeverity.WARNING,
        "conditions": [
            {
                "field": "function_name",
                "operator": "regex",
                "value": r"^[a-z][a-z0-9_]*$",
                "case_sensitive": True
            }
        ],
        "actions": {
            "type": "warn",
            "message": "Function names should use snake_case convention",
            "suggestions": ["Convert to snake_case", "Use lowercase with underscores"]
        }
    },
    {
        "name": "File Size Limit",
        "description": "Warn about large files that may need refactoring",
        "rule_type": RuleType.GLOBAL,
        "category": RuleCategory.CODE_QUALITY,
        "severity": RuleSeverity.WARNING,
        "conditions": [
            {
                "field": "file_lines",
                "operator": "greater_than",
                "value": 500,
                "case_sensitive": False
            }
        ],
        "actions": {
            "type": "warn",
            "message": "File is getting large. Consider breaking into smaller modules.",
            "suggestions": ["Split into multiple files", "Extract classes/functions", "Use modules"]
        }
    }
]
