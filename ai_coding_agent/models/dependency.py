"""
Dependency Engine Models
Implements data structures for Phase B2: Dependency Engine & Phase Locking.
"""

from datetime import datetime, timezone
from typing import Dict, List, Optional, Literal, Any
from enum import Enum

from pydantic import BaseModel, Field


class DependencyCheckStatus(str, Enum):
    """Status of dependency check."""
    CAN_START = "can_start"
    BLOCKED = "blocked"
    WARNING = "warning"
    OVERRIDE_REQUIRED = "override_required"


class DependencyType(str, Enum):
    """Type of dependency relationship."""
    TASK = "task"
    STEP = "step"
    PHASE = "phase"
    EXTERNAL = "external"
    EXTERNAL_API = "external_api"
    FILE_DEPENDENCY = "file_dependency"
    ENVIRONMENT = "environment"
    APPROVAL = "approval"
    TIME_BASED = "time_based"


class OverrideLevel(str, Enum):
    """Level of override permission required."""
    NONE = "none"
    WARNING = "warning"
    DEVELOPER = "developer"
    ADMIN = "admin"


class BlockingDependency(BaseModel):
    """Represents a single blocking dependency."""
    dependency_id: str = Field(..., description="ID of the blocking dependency")
    dependency_type: DependencyType = Field(..., description="Type of dependency")
    dependency_name: str = Field(..., description="Human-readable name of the dependency")
    current_status: str = Field(..., description="Current status of the dependency")
    required_status: str = Field(default="completed", description="Required status to unblock")
    estimated_completion: Optional[datetime] = Field(None, description="Estimated completion time")
    blocking_reason: str = Field(..., description="Reason why this dependency is blocking")


class DependencyCheckResult(BaseModel):
    """Result of a dependency check operation."""
    entity_id: str = Field(..., description="ID of the entity being checked")
    entity_type: DependencyType = Field(..., description="Type of entity")
    entity_name: str = Field(..., description="Human-readable name of the entity")
    status: DependencyCheckStatus = Field(..., description="Overall dependency check status")
    can_start: bool = Field(..., description="Whether the entity can start execution")
    blocking_dependencies: List[BlockingDependency] = Field(
        default_factory=list,
        description="List of dependencies that are blocking execution"
    )
    warnings: List[str] = Field(
        default_factory=list,
        description="Non-blocking warnings about dependencies"
    )
    override_level: OverrideLevel = Field(
        default=OverrideLevel.NONE,
        description="Level of override required to proceed"
    )
    message: str = Field(..., description="Human-readable status message")
    checked_at: datetime = Field(default_factory=lambda: datetime.now(timezone.utc), description="When the check was performed")


class PhaseProgressionResult(BaseModel):
    """Result of phase progression validation."""
    phase_id: str = Field(..., description="ID of the phase")
    phase_name: str = Field(..., description="Name of the phase")
    can_progress: bool = Field(..., description="Whether the phase can progress to next")
    completion_percentage: float = Field(..., description="Completion percentage (0-100)")
    completed_steps: int = Field(..., description="Number of completed steps")
    total_steps: int = Field(..., description="Total number of steps")
    completed_tasks: int = Field(..., description="Number of completed tasks")
    total_tasks: int = Field(..., description="Total number of tasks")
    blocking_items: List[Dict[str, Any]] = Field(
        default_factory=list,
        description="Items preventing progression"
    )
    next_phase_id: Optional[str] = Field(None, description="ID of the next phase if progression is possible")
    estimated_completion: Optional[datetime] = Field(None, description="Estimated completion time")


class DependencyOverride(BaseModel):
    """Represents a dependency override request or record."""
    entity_id: str = Field(..., description="ID of the entity being overridden")
    entity_type: DependencyType = Field(..., description="Type of entity")
    override_level: OverrideLevel = Field(..., description="Level of override being applied")
    reason: str = Field(..., description="Reason for the override")
    requested_by: str = Field(..., description="User ID who requested the override")
    approved_by: Optional[str] = Field(None, description="User ID who approved the override")
    expires_at: Optional[datetime] = Field(None, description="When the override expires")
    created_at: datetime = Field(default_factory=lambda: datetime.now(timezone.utc), description="When the override was created")
    is_active: bool = Field(default=True, description="Whether the override is currently active")


class DependencyValidationRequest(BaseModel):
    """Request for dependency validation."""
    entity_id: str = Field(..., description="ID of the entity to validate")
    entity_type: DependencyType = Field(..., description="Type of entity")
    operation: Literal["start", "complete", "skip"] = Field(..., description="Operation being attempted")
    force_override: bool = Field(default=False, description="Whether to force execution despite dependencies")
    override_reason: Optional[str] = Field(None, description="Reason for override if force_override is True")
    user_id: str = Field(..., description="ID of the user making the request")


class DependencyValidationResponse(BaseModel):
    """Response from dependency validation."""
    validation_id: str = Field(..., description="Unique ID for this validation")
    request: DependencyValidationRequest = Field(..., description="Original request")
    result: DependencyCheckResult = Field(..., description="Dependency check result")
    allowed: bool = Field(..., description="Whether the operation is allowed")
    requires_override: bool = Field(..., description="Whether an override is required")
    override_applied: Optional[DependencyOverride] = Field(None, description="Override that was applied")
    warnings: List[str] = Field(default_factory=list, description="Validation warnings")
    errors: List[str] = Field(default_factory=list, description="Validation errors")


class StatusBubbleEvent(BaseModel):
    """Event for status bubbling up the hierarchy."""
    source_entity_id: str = Field(..., description="ID of the entity that changed status")
    source_entity_type: DependencyType = Field(..., description="Type of source entity")
    old_status: str = Field(..., description="Previous status")
    new_status: str = Field(..., description="New status")
    triggered_changes: List[Dict[str, Any]] = Field(
        default_factory=list,
        description="Changes triggered by this status change"
    )
    propagation_path: List[str] = Field(
        default_factory=list,
        description="Path of entities affected by status propagation"
    )
    timestamp: datetime = Field(default_factory=lambda: datetime.now(timezone.utc), description="When the event occurred")
    user_id: Optional[str] = Field(None, description="User who triggered the change")


class DependencyGraph(BaseModel):
    """Represents the dependency graph for analysis."""
    entities: Dict[str, Dict[str, Any]] = Field(
        default_factory=dict,
        description="Map of entity_id to entity details"
    )
    dependencies: Dict[str, List[str]] = Field(
        default_factory=dict,
        description="Map of entity_id to list of dependency_ids"
    )
    reverse_dependencies: Dict[str, List[str]] = Field(
        default_factory=dict,
        description="Map of entity_id to list of entities that depend on it"
    )
    cycles: List[List[str]] = Field(
        default_factory=list,
        description="List of dependency cycles detected"
    )
    critical_path: List[str] = Field(
        default_factory=list,
        description="Critical path through the dependency graph"
    )


class ConditionType(str, Enum):
    """Type of condition for conditional dependencies."""
    CODE = "code"
    ENVIRONMENT = "environment"
    USER_CHOICE = "user_choice"
    PLATFORM = "platform"
    FEATURE_FLAG = "feature_flag"
    TIME_CONDITION = "time_condition"


class ConditionalDependency(BaseModel):
    """Represents a dependency that only applies under certain conditions."""
    dependency_id: str = Field(..., description="ID of the dependency")
    dependency_type: DependencyType = Field(..., description="Type of dependency")
    condition: str = Field(..., description="Condition expression (e.g., 'platform == web')")
    condition_type: ConditionType = Field(..., description="Type of condition")
    condition_context: Dict[str, Any] = Field(
        default_factory=dict,
        description="Additional context for condition evaluation"
    )
    description: str = Field(..., description="Human-readable description of the condition")
    is_active: bool = Field(default=True, description="Whether this conditional dependency is active")
    created_at: datetime = Field(default_factory=lambda: datetime.now(timezone.utc), description="When the condition was created")


class ConditionEvaluationContext(BaseModel):
    """Context for evaluating conditional dependencies."""
    platform: Optional[str] = Field(None, description="Target platform (web, mobile, desktop)")
    environment: Optional[str] = Field(None, description="Environment (dev, staging, prod)")
    feature_flags: Dict[str, bool] = Field(default_factory=dict, description="Feature flag states")
    user_choices: Dict[str, Any] = Field(default_factory=dict, description="User-selected options")
    code_context: Dict[str, Any] = Field(default_factory=dict, description="Code analysis context")
    time_context: Dict[str, Any] = Field(default_factory=dict, description="Time-based context")
    custom_variables: Dict[str, Any] = Field(default_factory=dict, description="Custom variables")


class ConditionEvaluationResult(BaseModel):
    """Result of evaluating a conditional dependency."""
    condition_id: str = Field(..., description="ID of the condition")
    dependency_id: str = Field(..., description="ID of the dependency")
    condition_met: bool = Field(..., description="Whether the condition is met")
    evaluation_details: str = Field(..., description="Details of the evaluation")
    context_used: ConditionEvaluationContext = Field(..., description="Context used for evaluation")
    evaluated_at: datetime = Field(default_factory=lambda: datetime.now(timezone.utc), description="When evaluation occurred")
    errors: List[str] = Field(default_factory=list, description="Any errors during evaluation")


class SoftDependency(BaseModel):
    """Represents a non-blocking dependency that affects quality/efficiency."""
    dependency_id: str = Field(..., description="ID of the dependency")
    dependency_type: DependencyType = Field(..., description="Type of dependency")
    preference_level: Literal["preferred", "optional", "nice_to_have"] = Field(
        ..., description="Level of preference for this dependency"
    )
    impact_if_missing: str = Field(..., description="Impact description if dependency is not met")
    quality_impact: float = Field(
        default=0.0,
        ge=0.0,
        le=1.0,
        description="Quality impact score (0-1) if dependency is missing"
    )
    performance_impact: float = Field(
        default=0.0,
        ge=0.0,
        le=1.0,
        description="Performance impact score (0-1) if dependency is missing"
    )
    created_at: datetime = Field(default_factory=lambda: datetime.now(timezone.utc), description="When the soft dependency was created")


class SoftDependencyResult(BaseModel):
    """Result of checking soft dependencies."""
    entity_id: str = Field(..., description="ID of the entity being checked")
    entity_type: DependencyType = Field(..., description="Type of entity")
    soft_dependencies: List[SoftDependency] = Field(
        default_factory=list,
        description="List of soft dependencies"
    )
    missing_soft_dependencies: List[SoftDependency] = Field(
        default_factory=list,
        description="List of missing soft dependencies"
    )
    overall_quality_impact: float = Field(
        default=0.0,
        description="Overall quality impact from missing soft dependencies"
    )
    overall_performance_impact: float = Field(
        default=0.0,
        description="Overall performance impact from missing soft dependencies"
    )
    recommendations: List[str] = Field(
        default_factory=list,
        description="Recommendations for addressing missing soft dependencies"
    )
    checked_at: datetime = Field(default_factory=lambda: datetime.now(timezone.utc), description="When the check was performed")


class ExternalDependencyStatus(str, Enum):
    """Status of external dependencies."""
    AVAILABLE = "available"
    UNAVAILABLE = "unavailable"
    DEGRADED = "degraded"
    UNKNOWN = "unknown"
    MAINTENANCE = "maintenance"


class ExternalDependency(BaseModel):
    """Represents an external dependency (API, service, file, etc.)."""
    dependency_id: str = Field(..., description="Unique ID for the external dependency")
    name: str = Field(..., description="Human-readable name")
    dependency_type: DependencyType = Field(..., description="Type of external dependency")
    endpoint_url: Optional[str] = Field(None, description="URL for API dependencies")
    file_path: Optional[str] = Field(None, description="File path for file dependencies")
    environment_variable: Optional[str] = Field(None, description="Environment variable name")
    check_method: str = Field(..., description="Method to check dependency status")
    check_interval: int = Field(default=300, description="Check interval in seconds")
    timeout: int = Field(default=30, description="Timeout for checks in seconds")
    retry_count: int = Field(default=3, description="Number of retries for failed checks")
    status: ExternalDependencyStatus = Field(default=ExternalDependencyStatus.UNKNOWN, description="Current status")
    last_checked: Optional[datetime] = Field(None, description="When last checked")
    last_success: Optional[datetime] = Field(None, description="When last successful")
    error_message: Optional[str] = Field(None, description="Last error message")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="Additional metadata")


class ApprovalDependency(BaseModel):
    """Represents a human approval dependency."""
    dependency_id: str = Field(..., description="Unique ID for the approval dependency")
    name: str = Field(..., description="Name of the approval")
    approvers: List[str] = Field(..., description="List of user IDs who can approve")
    approval_type: Literal["any", "all", "majority"] = Field(
        default="any", description="Type of approval required"
    )
    description: str = Field(..., description="Description of what needs approval")
    approval_criteria: Optional[str] = Field(None, description="Specific criteria for approval")
    auto_approve_conditions: Optional[str] = Field(None, description="Conditions for auto-approval")
    deadline: Optional[datetime] = Field(None, description="Deadline for approval")
    status: Literal["pending", "approved", "rejected", "expired"] = Field(
        default="pending", description="Current approval status"
    )
    approved_by: List[str] = Field(default_factory=list, description="List of users who approved")
    rejected_by: List[str] = Field(default_factory=list, description="List of users who rejected")
    approval_date: Optional[datetime] = Field(None, description="When approved")
    rejection_reason: Optional[str] = Field(None, description="Reason for rejection")
    created_at: datetime = Field(default_factory=lambda: datetime.now(timezone.utc), description="When created")


class TimeDependency(BaseModel):
    """Represents a time-based dependency."""
    dependency_id: str = Field(..., description="Unique ID for the time dependency")
    name: str = Field(..., description="Name of the time dependency")
    time_type: Literal["absolute", "relative", "recurring"] = Field(
        ..., description="Type of time dependency"
    )
    target_time: Optional[datetime] = Field(None, description="Target time for absolute dependencies")
    relative_to_entity: Optional[str] = Field(None, description="Entity ID for relative dependencies")
    relative_offset: Optional[int] = Field(None, description="Offset in seconds for relative dependencies")
    recurring_pattern: Optional[str] = Field(None, description="Cron pattern for recurring dependencies")
    timezone: str = Field(default="UTC", description="Timezone for time calculations")
    grace_period: int = Field(default=0, description="Grace period in seconds")
    status: Literal["pending", "ready", "expired"] = Field(default="pending", description="Current status")
    next_available: Optional[datetime] = Field(None, description="Next time this dependency will be ready")
    created_at: datetime = Field(default_factory=lambda: datetime.now(timezone.utc), description="When created")


class BatchDependencyCheckRequest(BaseModel):
    """Request for batch dependency checking."""
    entity_ids: List[str] = Field(..., description="List of entity IDs to check")
    entity_type: DependencyType = Field(..., description="Type of entities")
    include_soft_dependencies: bool = Field(default=False, description="Whether to include soft dependencies")
    include_conditional_dependencies: bool = Field(default=True, description="Whether to evaluate conditional dependencies")
    evaluation_context: Optional[ConditionEvaluationContext] = Field(
        None, description="Context for conditional dependency evaluation"
    )
    user_id: str = Field(..., description="ID of the user making the request")


class BatchDependencyCheckResponse(BaseModel):
    """Response from batch dependency checking."""
    request_id: str = Field(..., description="Unique ID for this batch request")
    results: Dict[str, DependencyCheckResult] = Field(
        default_factory=dict,
        description="Map of entity_id to dependency check result"
    )
    soft_dependency_results: Dict[str, SoftDependencyResult] = Field(
        default_factory=dict,
        description="Map of entity_id to soft dependency result"
    )
    conditional_evaluations: Dict[str, List[ConditionEvaluationResult]] = Field(
        default_factory=dict,
        description="Map of entity_id to conditional dependency evaluations"
    )
    processing_time: float = Field(..., description="Time taken to process the batch in seconds")
    errors: List[str] = Field(default_factory=list, description="Any errors during batch processing")
    processed_at: datetime = Field(default_factory=lambda: datetime.now(timezone.utc), description="When the batch was processed")


class DependencyBottleneck(BaseModel):
    """Represents a dependency bottleneck."""
    entity_id: str = Field(..., description="ID of the bottleneck entity")
    entity_type: DependencyType = Field(..., description="Type of entity")
    entity_name: str = Field(..., description="Name of the entity")
    bottleneck_type: Literal["blocking", "resource", "approval", "external"] = Field(
        ..., description="Type of bottleneck"
    )
    severity: Literal["low", "medium", "high", "critical"] = Field(..., description="Severity of bottleneck")
    affected_entities: List[str] = Field(default_factory=list, description="Entities affected by this bottleneck")
    estimated_delay: Optional[int] = Field(None, description="Estimated delay in hours")
    resolution_suggestions: List[str] = Field(default_factory=list, description="Suggestions to resolve bottleneck")
    detected_at: datetime = Field(default_factory=lambda: datetime.now(timezone.utc), description="When bottleneck was detected")


class DependencyMetrics(BaseModel):
    """Dependency system metrics."""
    roadmap_id: str = Field(..., description="ID of the roadmap")
    timeframe_start: datetime = Field(..., description="Start of metrics timeframe")
    timeframe_end: datetime = Field(..., description="End of metrics timeframe")

    # Dependency resolution metrics
    total_dependencies_checked: int = Field(default=0, description="Total dependencies checked")
    dependencies_resolved: int = Field(default=0, description="Dependencies resolved")
    dependencies_blocked: int = Field(default=0, description="Dependencies currently blocked")
    average_resolution_time: float = Field(default=0.0, description="Average resolution time in hours")

    # Override metrics
    total_overrides: int = Field(default=0, description="Total overrides used")
    override_success_rate: float = Field(default=0.0, description="Success rate of overrides")

    # Bottleneck metrics
    bottlenecks_detected: int = Field(default=0, description="Number of bottlenecks detected")
    critical_bottlenecks: int = Field(default=0, description="Number of critical bottlenecks")

    # Performance metrics
    average_check_time: float = Field(default=0.0, description="Average dependency check time in ms")
    cache_hit_rate: float = Field(default=0.0, description="Cache hit rate percentage")

    # Prediction accuracy
    ai_prediction_accuracy: float = Field(default=0.0, description="AI prediction accuracy percentage")

    generated_at: datetime = Field(default_factory=lambda: datetime.now(timezone.utc), description="When metrics were generated")


class DelayPrediction(BaseModel):
    """Prediction of potential project delays."""
    entity_id: str = Field(..., description="ID of the entity")
    entity_type: DependencyType = Field(..., description="Type of entity")
    entity_name: str = Field(..., description="Name of the entity")
    predicted_delay: int = Field(..., description="Predicted delay in hours")
    confidence: float = Field(..., ge=0.0, le=1.0, description="Confidence in prediction (0-1)")
    delay_causes: List[str] = Field(default_factory=list, description="Causes of the predicted delay")
    mitigation_strategies: List[str] = Field(default_factory=list, description="Strategies to mitigate delay")
    impact_on_project: str = Field(..., description="Impact on overall project timeline")
    predicted_at: datetime = Field(default_factory=lambda: datetime.now(timezone.utc), description="When prediction was made")


class DependencyAnalyticsReport(BaseModel):
    """Comprehensive dependency analytics report."""
    roadmap_id: str = Field(..., description="ID of the roadmap")
    report_type: Literal["daily", "weekly", "monthly", "custom"] = Field(..., description="Type of report")
    timeframe_start: datetime = Field(..., description="Start of report timeframe")
    timeframe_end: datetime = Field(..., description="End of report timeframe")

    metrics: DependencyMetrics = Field(..., description="Dependency metrics")
    bottlenecks: List[DependencyBottleneck] = Field(default_factory=list, description="Detected bottlenecks")
    delay_predictions: List[DelayPrediction] = Field(default_factory=list, description="Delay predictions")

    # Summary insights
    key_insights: List[str] = Field(default_factory=list, description="Key insights from analysis")
    recommendations: List[str] = Field(default_factory=list, description="Recommendations for improvement")
    risk_factors: List[str] = Field(default_factory=list, description="Identified risk factors")

    # Trends
    dependency_trends: Dict[str, Any] = Field(default_factory=dict, description="Dependency trends over time")
    performance_trends: Dict[str, Any] = Field(default_factory=dict, description="Performance trends")

    generated_at: datetime = Field(default_factory=lambda: datetime.now(timezone.utc), description="When report was generated")
    generated_by: str = Field(..., description="User or system that generated the report")


class VisualizationNode(BaseModel):
    """Node in a dependency visualization."""
    id: str = Field(..., description="Node ID")
    label: str = Field(..., description="Node label")
    type: DependencyType = Field(..., description="Node type")
    status: str = Field(..., description="Node status")
    x: Optional[float] = Field(None, description="X coordinate")
    y: Optional[float] = Field(None, description="Y coordinate")
    color: Optional[str] = Field(None, description="Node color")
    size: Optional[int] = Field(None, description="Node size")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="Additional metadata")


class VisualizationEdge(BaseModel):
    """Edge in a dependency visualization."""
    source: str = Field(..., description="Source node ID")
    target: str = Field(..., description="Target node ID")
    type: Literal["dependency", "blocking", "conditional", "soft"] = Field(..., description="Edge type")
    weight: Optional[float] = Field(None, description="Edge weight")
    color: Optional[str] = Field(None, description="Edge color")
    style: Optional[str] = Field(None, description="Edge style (solid, dashed, dotted)")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="Additional metadata")


class DependencyGraphVisualization(BaseModel):
    """Dependency graph visualization data."""
    roadmap_id: str = Field(..., description="ID of the roadmap")
    title: str = Field(..., description="Visualization title")
    nodes: List[VisualizationNode] = Field(default_factory=list, description="Graph nodes")
    edges: List[VisualizationEdge] = Field(default_factory=list, description="Graph edges")
    layout: Literal["hierarchical", "force", "circular", "grid"] = Field(
        default="hierarchical", description="Layout algorithm"
    )
    filters: Dict[str, Any] = Field(default_factory=dict, description="Applied filters")
    zoom_level: float = Field(default=1.0, description="Zoom level")
    center_x: float = Field(default=0.0, description="Center X coordinate")
    center_y: float = Field(default=0.0, description="Center Y coordinate")
    generated_at: datetime = Field(default_factory=lambda: datetime.now(timezone.utc), description="When visualization was generated")


class GanttChartTask(BaseModel):
    """Task representation in Gantt chart."""
    id: str = Field(..., description="Task ID")
    name: str = Field(..., description="Task name")
    start_date: datetime = Field(..., description="Task start date")
    end_date: datetime = Field(..., description="Task end date")
    duration: int = Field(..., description="Duration in hours")
    progress: float = Field(default=0.0, ge=0.0, le=1.0, description="Progress percentage (0-1)")
    dependencies: List[str] = Field(default_factory=list, description="Dependency task IDs")
    assigned_to: Optional[str] = Field(None, description="Assigned user")
    priority: Literal["low", "medium", "high", "critical"] = Field(default="medium", description="Task priority")
    status: str = Field(..., description="Task status")
    color: Optional[str] = Field(None, description="Task color in chart")
    parent_id: Optional[str] = Field(None, description="Parent task/step/phase ID")


class GanttChart(BaseModel):
    """Gantt chart visualization data."""
    roadmap_id: str = Field(..., description="ID of the roadmap")
    title: str = Field(..., description="Chart title")
    tasks: List[GanttChartTask] = Field(default_factory=list, description="Chart tasks")
    start_date: datetime = Field(..., description="Chart start date")
    end_date: datetime = Field(..., description="Chart end date")
    time_scale: Literal["hours", "days", "weeks", "months"] = Field(
        default="days", description="Time scale"
    )
    show_dependencies: bool = Field(default=True, description="Whether to show dependency lines")
    show_critical_path: bool = Field(default=True, description="Whether to highlight critical path")
    filters: Dict[str, Any] = Field(default_factory=dict, description="Applied filters")
    generated_at: datetime = Field(default_factory=lambda: datetime.now(timezone.utc), description="When chart was generated")


class DashboardWidget(BaseModel):
    """Widget in a dependency dashboard."""
    id: str = Field(..., description="Widget ID")
    type: Literal["chart", "metric", "list", "graph", "table"] = Field(..., description="Widget type")
    title: str = Field(..., description="Widget title")
    data: Dict[str, Any] = Field(default_factory=dict, description="Widget data")
    position: Dict[str, int] = Field(default_factory=dict, description="Widget position (x, y, width, height)")
    refresh_interval: Optional[int] = Field(None, description="Auto-refresh interval in seconds")
    filters: Dict[str, Any] = Field(default_factory=dict, description="Widget-specific filters")


class DependencyDashboard(BaseModel):
    """Dependency management dashboard."""
    id: str = Field(..., description="Dashboard ID")
    name: str = Field(..., description="Dashboard name")
    roadmap_id: str = Field(..., description="ID of the roadmap")
    user_id: str = Field(..., description="Owner user ID")
    widgets: List[DashboardWidget] = Field(default_factory=list, description="Dashboard widgets")
    layout: Literal["grid", "free"] = Field(default="grid", description="Dashboard layout")
    theme: Literal["light", "dark", "auto"] = Field(default="light", description="Dashboard theme")
    auto_refresh: bool = Field(default=True, description="Whether to auto-refresh data")
    refresh_interval: int = Field(default=300, description="Auto-refresh interval in seconds")
    is_public: bool = Field(default=False, description="Whether dashboard is publicly accessible")
    created_at: datetime = Field(default_factory=lambda: datetime.now(timezone.utc), description="When dashboard was created")
    updated_at: datetime = Field(default_factory=lambda: datetime.now(timezone.utc), description="When dashboard was last updated")


class ExternalToolType(str, Enum):
    """Type of external tool integration."""
    JIRA = "jira"
    GITHUB = "github"
    GITLAB = "gitlab"
    AZURE_DEVOPS = "azure_devops"
    TRELLO = "trello"
    ASANA = "asana"
    CI_CD = "ci_cd"
    SLACK = "slack"
    TEAMS = "teams"


class ExternalToolConfig(BaseModel):
    """Configuration for external tool integration."""
    tool_type: ExternalToolType = Field(..., description="Type of external tool")
    name: str = Field(..., description="Name of the integration")
    base_url: str = Field(..., description="Base URL for the tool")
    api_key: Optional[str] = Field(None, description="API key for authentication")
    username: Optional[str] = Field(None, description="Username for authentication")
    token: Optional[str] = Field(None, description="Token for authentication")
    project_id: Optional[str] = Field(None, description="Project ID in the external tool")
    workspace_id: Optional[str] = Field(None, description="Workspace ID in the external tool")
    webhook_url: Optional[str] = Field(None, description="Webhook URL for notifications")
    sync_enabled: bool = Field(default=True, description="Whether sync is enabled")
    sync_interval: int = Field(default=300, description="Sync interval in seconds")
    field_mappings: Dict[str, str] = Field(
        default_factory=dict,
        description="Field mappings between systems"
    )
    filters: Dict[str, Any] = Field(
        default_factory=dict,
        description="Filters for syncing data"
    )
    created_at: datetime = Field(default_factory=lambda: datetime.now(timezone.utc), description="When config was created")
    updated_at: datetime = Field(default_factory=lambda: datetime.now(timezone.utc), description="When config was last updated")


class ExternalToolSyncStatus(str, Enum):
    """Status of external tool sync."""
    SUCCESS = "success"
    FAILED = "failed"
    IN_PROGRESS = "in_progress"
    PARTIAL = "partial"
    SKIPPED = "skipped"


class ExternalToolSyncResult(BaseModel):
    """Result of external tool synchronization."""
    tool_config_id: str = Field(..., description="ID of the tool configuration")
    sync_type: Literal["import", "export", "bidirectional"] = Field(..., description="Type of sync")
    status: ExternalToolSyncStatus = Field(..., description="Sync status")
    items_processed: int = Field(default=0, description="Number of items processed")
    items_created: int = Field(default=0, description="Number of items created")
    items_updated: int = Field(default=0, description="Number of items updated")
    items_failed: int = Field(default=0, description="Number of items that failed")
    errors: List[str] = Field(default_factory=list, description="Sync errors")
    warnings: List[str] = Field(default_factory=list, description="Sync warnings")
    sync_duration: float = Field(default=0.0, description="Sync duration in seconds")
    started_at: datetime = Field(default_factory=lambda: datetime.now(timezone.utc), description="When sync started")
    completed_at: Optional[datetime] = Field(None, description="When sync completed")
    next_sync_at: Optional[datetime] = Field(None, description="When next sync is scheduled")


class JiraIssueMapping(BaseModel):
    """Mapping between internal tasks and JIRA issues."""
    task_id: str = Field(..., description="Internal task ID")
    jira_issue_key: str = Field(..., description="JIRA issue key")
    jira_issue_id: str = Field(..., description="JIRA issue ID")
    sync_direction: Literal["import", "export", "bidirectional"] = Field(..., description="Sync direction")
    last_synced: Optional[datetime] = Field(None, description="When last synced")
    sync_status: ExternalToolSyncStatus = Field(default=ExternalToolSyncStatus.SUCCESS, description="Sync status")
    field_mappings: Dict[str, str] = Field(default_factory=dict, description="Field mappings")
    created_at: datetime = Field(default_factory=lambda: datetime.now(timezone.utc), description="When mapping was created")


class GitHubIssueMapping(BaseModel):
    """Mapping between internal tasks and GitHub issues."""
    task_id: str = Field(..., description="Internal task ID")
    github_issue_number: int = Field(..., description="GitHub issue number")
    github_repo: str = Field(..., description="GitHub repository (owner/repo)")
    sync_direction: Literal["import", "export", "bidirectional"] = Field(..., description="Sync direction")
    last_synced: Optional[datetime] = Field(None, description="When last synced")
    sync_status: ExternalToolSyncStatus = Field(default=ExternalToolSyncStatus.SUCCESS, description="Sync status")
    field_mappings: Dict[str, str] = Field(default_factory=dict, description="Field mappings")
    created_at: datetime = Field(default_factory=lambda: datetime.now(timezone.utc), description="When mapping was created")


class CICDPipelineMapping(BaseModel):
    """Mapping between internal tasks and CI/CD pipelines."""
    task_id: str = Field(..., description="Internal task ID")
    pipeline_id: str = Field(..., description="CI/CD pipeline ID")
    pipeline_name: str = Field(..., description="Pipeline name")
    pipeline_url: str = Field(..., description="Pipeline URL")
    trigger_on_status: List[str] = Field(
        default_factory=list,
        description="Task statuses that trigger the pipeline"
    )
    dependency_on_success: bool = Field(
        default=False,
        description="Whether task depends on pipeline success"
    )
    last_triggered: Optional[datetime] = Field(None, description="When pipeline was last triggered")
    last_status: Optional[str] = Field(None, description="Last pipeline status")
    created_at: datetime = Field(default_factory=lambda: datetime.now(timezone.utc), description="When mapping was created")


class ExternalToolIntegration(BaseModel):
    """External tool integration configuration and status."""
    id: str = Field(..., description="Integration ID")
    roadmap_id: str = Field(..., description="Associated roadmap ID")
    tool_config: ExternalToolConfig = Field(..., description="Tool configuration")
    is_active: bool = Field(default=True, description="Whether integration is active")
    last_sync_result: Optional[ExternalToolSyncResult] = Field(None, description="Last sync result")
    sync_schedule: Optional[str] = Field(None, description="Cron schedule for automatic sync")
    error_threshold: int = Field(default=5, description="Error threshold before disabling")
    consecutive_errors: int = Field(default=0, description="Consecutive error count")
    created_by: str = Field(..., description="User who created the integration")
    created_at: datetime = Field(default_factory=datetime.utcnow, description="When integration was created")
    updated_at: datetime = Field(default_factory=datetime.utcnow, description="When integration was last updated")
