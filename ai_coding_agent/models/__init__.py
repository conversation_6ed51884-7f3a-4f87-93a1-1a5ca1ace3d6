"""
Database models for AI Coding Agent.

This module contains all the database models and schemas.
"""

from .base import Base, get_db, create_tables, drop_tables
from .user import User, UserCreate, UserUpdate, UserResponse, UserLogin
from .roadmap import (
    # SQLAlchemy Models
    Project,
    Roadmap,
    Phase,
    Step,
    Task,
    RoadmapVersion,
    TaskStatus,
    AgentType,
    # Pydantic Schemas
    TaskArtifact,
    TaskCreate,
    TaskUpdate,
    TaskResponse,
    StepCreate,
    StepUpdate,
    StepResponse,
    PhaseCreate,
    PhaseUpdate,
    PhaseResponse,
    RoadmapCreate,
    RoadmapUpdate,
    RoadmapResponse,
    ProjectCreate,
    ProjectUpdate,
    ProjectResponse,
    # Versioning Schemas
    RoadmapVersionCreate,
    RoadmapVersionUpdate,
    RoadmapVersionResponse,
    VersionComparison,
)
from .audit import (
    # SQLAlchemy Models
    AuditLog,
    StatusHistory,
    ConcurrencyControl,
    AuditAction,
    AuditEntityType,
    # Pydantic Schemas
    AuditLogResponse,
    StatusHistoryResponse,
    ConcurrencyControlResponse,
    AuditLogFilter,
    ConflictResolution,
)
from .dependency import (
    # Enums
    DependencyCheckStatus,
    DependencyType,
    OverrideLevel,
    ConditionType,
    ExternalDependencyStatus,
    # Pydantic Schemas
    BlockingDependency,
    DependencyCheckResult,
    PhaseProgressionResult,
    DependencyOverride,
    DependencyValidationRequest,
    DependencyValidationResponse,
    StatusBubbleEvent,
    DependencyGraph,
    ConditionalDependency,
    ConditionEvaluationContext,
    ConditionEvaluationResult,
    SoftDependency,
    SoftDependencyResult,
    ExternalDependency,
    ApprovalDependency,
    TimeDependency,
    BatchDependencyCheckRequest,
    BatchDependencyCheckResponse,
    DependencyBottleneck,
    DependencyMetrics,
    DelayPrediction,
    DependencyAnalyticsReport,
    VisualizationNode,
    VisualizationEdge,
    DependencyGraphVisualization,
    GanttChartTask,
    GanttChart,
    DashboardWidget,
    DependencyDashboard,
    ExternalToolType,
    ExternalToolConfig,
    ExternalToolSyncStatus,
    ExternalToolSyncResult,
    JiraIssueMapping,
    GitHubIssueMapping,
    CICDPipelineMapping,
    ExternalToolIntegration,
)

__all__ = [
    "Base",
    "get_db",
    "create_tables",
    "drop_tables",
    "User",
    "UserCreate",
    "UserUpdate",
    "UserResponse",
    "UserLogin",
    # Roadmap Models
    "Project",
    "Roadmap",
    "Phase",
    "Step",
    "Task",
    "RoadmapVersion",
    "TaskStatus",
    "AgentType",
    # Roadmap Schemas
    "TaskArtifact",
    "TaskCreate",
    "TaskUpdate",
    "TaskResponse",
    "StepCreate",
    "StepUpdate",
    "StepResponse",
    "PhaseCreate",
    "PhaseUpdate",
    "PhaseResponse",
    "RoadmapCreate",
    "RoadmapUpdate",
    "RoadmapResponse",
    "ProjectCreate",
    "ProjectUpdate",
    "ProjectResponse",
    # Versioning Schemas
    "RoadmapVersionCreate",
    "RoadmapVersionUpdate",
    "RoadmapVersionResponse",
    "VersionComparison",
    # Audit Models
    "AuditLog",
    "StatusHistory",
    "ConcurrencyControl",
    "AuditAction",
    "AuditEntityType",
    # Audit Schemas
    "AuditLogResponse",
    "StatusHistoryResponse",
    "ConcurrencyControlResponse",
    "AuditLogFilter",
    "ConflictResolution",
    # Dependency Models
    "DependencyCheckStatus",
    "DependencyType",
    "OverrideLevel",
    "ConditionType",
    "ExternalDependencyStatus",
    "BlockingDependency",
    "DependencyCheckResult",
    "PhaseProgressionResult",
    "DependencyOverride",
    "DependencyValidationRequest",
    "DependencyValidationResponse",
    "StatusBubbleEvent",
    "DependencyGraph",
    "ConditionalDependency",
    "ConditionEvaluationContext",
    "ConditionEvaluationResult",
    "SoftDependency",
    "SoftDependencyResult",
    "ExternalDependency",
    "ApprovalDependency",
    "TimeDependency",
    "BatchDependencyCheckRequest",
    "BatchDependencyCheckResponse",
    "DependencyBottleneck",
    "DependencyMetrics",
    "DelayPrediction",
    "DependencyAnalyticsReport",
    "VisualizationNode",
    "VisualizationEdge",
    "DependencyGraphVisualization",
    "GanttChartTask",
    "GanttChart",
    "DashboardWidget",
    "DependencyDashboard",
    "ExternalToolType",
    "ExternalToolConfig",
    "ExternalToolSyncStatus",
    "ExternalToolSyncResult",
    "JiraIssueMapping",
    "GitHubIssueMapping",
    "CICDPipelineMapping",
    "ExternalToolIntegration",
]
