"""
Hybrid Database base configuration for SQLAlchemy + Supabase.

This module provides support for hybrid database architecture:
- SQLite/PostgreSQL for local tables (fast, frequent operations)
- Supabase for cloud tables (shared knowledge, long-term storage)
"""

from sqlalchemy import create_engine
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker
from typing import Generator, Dict, List
import logging

from ..config import settings

logger = logging.getLogger(__name__)

# Create declarative base for local models
Base = declarative_base()

class HybridDatabaseManager:
    """Manages hybrid database connections and routing."""

    def __init__(self):
        self.mode = settings.hybrid_db.mode
        self.local_tables = settings.hybrid_db.local_table_list
        self.supabase_tables = settings.hybrid_db.supabase_table_list

        # Initialize local database engine
        self._setup_local_engine()

        # Initialize Supabase connection if needed
        self._supabase_service = None
        if self.mode in ["supabase", "hybrid"]:
            self._setup_supabase()

    def _setup_local_engine(self):
        """Set up local database engine (SQLite or PostgreSQL)."""
        if self.mode == "sqlite":
            database_url = settings.hybrid_db.sqlite_url
        elif self.mode in ["postgresql", "hybrid"]:
            # Use PostgreSQL for hybrid mode local storage
            database_url = settings.database.url
        else:
            # Default to SQLite for supabase-only mode testing
            database_url = settings.hybrid_db.sqlite_url

        logger.info(f"Setting up local database: {database_url}")

        self.local_engine = create_engine(
            database_url,
            echo=settings.debug,
            pool_pre_ping=True,
            pool_recycle=300,
        )

        self.LocalSessionLocal = sessionmaker(
            autocommit=False,
            autoflush=False,
            bind=self.local_engine
        )

    def _setup_supabase(self):
        """Set up Supabase connection for cloud tables."""
        try:
            from ..services.supabase import get_supabase_service
            self._supabase_service = get_supabase_service()
            logger.info("Supabase connection initialized")
        except Exception as e:
            logger.warning(f"Failed to initialize Supabase: {e}")
            if self.mode == "supabase":
                raise

    def get_local_db(self) -> Generator:
        """Get local database session."""
        db = self.LocalSessionLocal()
        try:
            yield db
        finally:
            db.close()

    def get_supabase_service(self):
        """Get Supabase service for cloud operations."""
        if self._supabase_service is None:
            raise RuntimeError("Supabase not available")
        return self._supabase_service

    def is_local_table(self, table_name: str) -> bool:
        """Check if table should be stored locally."""
        return table_name in self.local_tables

    def is_supabase_table(self, table_name: str) -> bool:
        """Check if table should be stored in Supabase."""
        return table_name in self.supabase_tables

    def create_local_tables(self) -> None:
        """Create all local database tables."""
        Base.metadata.create_all(bind=self.local_engine)
        logger.info("Local tables created")

    def drop_local_tables(self) -> None:
        """Drop all local database tables (for testing)."""
        Base.metadata.drop_all(bind=self.local_engine)
        logger.info("Local tables dropped")

    def get_connection_status(self) -> Dict:
        """Get connection status for both databases."""
        status = {
            "mode": self.mode,
            "local_db": "disconnected",
            "supabase": "not_configured"
        }

        # Test local connection
        try:
            with self.local_engine.connect() as conn:
                from sqlalchemy import text
                conn.execute(text("SELECT 1"))
            status["local_db"] = "connected"
        except Exception as e:
            status["local_db"] = f"error: {str(e)}"

        # Test Supabase connection
        if self._supabase_service:
            try:
                supabase_status = self._supabase_service.get_connection_status()
                status["supabase"] = supabase_status.get("status", "unknown")
            except Exception as e:
                status["supabase"] = f"error: {str(e)}"

        return status

# Global hybrid database manager
_hybrid_db_manager = None

def get_hybrid_db_manager() -> HybridDatabaseManager:
    """Get or create hybrid database manager."""
    global _hybrid_db_manager
    if _hybrid_db_manager is None:
        _hybrid_db_manager = HybridDatabaseManager()
    return _hybrid_db_manager

# Convenience functions for backward compatibility
def get_db() -> Generator:
    """Get local database session (backward compatibility)."""
    manager = get_hybrid_db_manager()
    yield from manager.get_local_db()

def create_tables() -> None:
    """Create all local database tables."""
    manager = get_hybrid_db_manager()
    manager.create_local_tables()

def drop_tables() -> None:
    """Drop all local database tables (for testing)."""
    manager = get_hybrid_db_manager()
    manager.drop_local_tables()
