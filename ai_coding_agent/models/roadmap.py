"""
Roadmap System Models
Implements the core roadmap data structures for Phase B1.
"""

from datetime import datetime, timezone
from typing import Dict, List, Optional, Literal, Any
from enum import Enum
from uuid import uuid4

from sqlalchemy import Column, String, Text, DateTime, ForeignKey, JSON, <PERSON>teger, <PERSON><PERSON><PERSON>
from sqlalchemy.orm import relationship, Mapped, mapped_column
from sqlalchemy.dialects.postgresql import UUID
from pydantic import BaseModel, Field, validator

from .base import Base


class TaskStatus(str, Enum):
    """Task execution status."""
    PENDING = "pending"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    FAILED = "failed"
    BLOCKED = "blocked"
    SKIPPED = "skipped"


class AgentType(str, Enum):
    """Available agent types for task assignment."""
    ARCHITECT = "architect"
    FRONTEND = "frontend"
    BACKEND = "backend"
    SHELL = "shell"
    ISSUE_FIX = "issue_fix"
    TEST = "test"


# SQLAlchemy Models

class Project(Base):
    """Project entity - top level container for roadmaps."""
    __tablename__ = "projects"

    id: Mapped[str] = mapped_column(UUID(as_uuid=False), primary_key=True, default=lambda: str(uuid4()))
    name: Mapped[str] = mapped_column(String(255), nullable=False)
    description: Mapped[Optional[str]] = mapped_column(Text, nullable=True)
    tech_stack: Mapped[Optional[Dict]] = mapped_column(JSON, nullable=True)
    project_rules: Mapped[Optional[Dict]] = mapped_column(JSON, nullable=True)
    created_at: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow)
    updated_at: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Relationships
    roadmap: Mapped[Optional["Roadmap"]] = relationship("Roadmap", back_populates="project", uselist=False)


class Roadmap(Base):
    """Main roadmap entity containing phases, steps, and tasks."""
    __tablename__ = "roadmaps"

    id: Mapped[str] = mapped_column(UUID(as_uuid=False), primary_key=True, default=lambda: str(uuid4()))
    project_id: Mapped[str] = mapped_column(UUID(as_uuid=False), ForeignKey("projects.id"), nullable=False)
    name: Mapped[str] = mapped_column(String(255), nullable=False, default="Project Roadmap")
    version: Mapped[str] = mapped_column(String(50), nullable=False, default="1.0.0")
    status: Mapped[str] = mapped_column(String(50), nullable=False, default=TaskStatus.PENDING)
    project_metadata: Mapped[Optional[Dict]] = mapped_column(JSON, nullable=True)
    created_at: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow)
    updated_at: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Relationships
    project: Mapped["Project"] = relationship("Project", back_populates="roadmap")
    phases: Mapped[List["Phase"]] = relationship("Phase", back_populates="roadmap", cascade="all, delete-orphan")
    versions: Mapped[List["RoadmapVersion"]] = relationship("RoadmapVersion", back_populates="roadmap", cascade="all, delete-orphan")


class Phase(Base):
    """Phase entity - major development stages."""
    __tablename__ = "phases"

    id: Mapped[str] = mapped_column(UUID(as_uuid=False), primary_key=True, default=lambda: str(uuid4()))
    roadmap_id: Mapped[str] = mapped_column(UUID(as_uuid=False), ForeignKey("roadmaps.id"), nullable=False)
    name: Mapped[str] = mapped_column(String(255), nullable=False)
    description: Mapped[Optional[str]] = mapped_column(Text, nullable=True)
    order_index: Mapped[int] = mapped_column(Integer, nullable=False)
    status: Mapped[str] = mapped_column(String(50), nullable=False, default=TaskStatus.PENDING)
    dependencies: Mapped[List[str]] = mapped_column(JSON, nullable=False, default=list)
    estimated_duration: Mapped[Optional[str]] = mapped_column(String(100), nullable=True)
    project_metadata: Mapped[Optional[Dict]] = mapped_column(JSON, nullable=True)
    created_at: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow)
    updated_at: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Relationships
    roadmap: Mapped["Roadmap"] = relationship("Roadmap", back_populates="phases")
    steps: Mapped[List["Step"]] = relationship("Step", back_populates="phase", cascade="all, delete-orphan")


class Step(Base):
    """Step entity - specific objectives within phases."""
    __tablename__ = "steps"

    id: Mapped[str] = mapped_column(UUID(as_uuid=False), primary_key=True, default=lambda: str(uuid4()))
    phase_id: Mapped[str] = mapped_column(UUID(as_uuid=False), ForeignKey("phases.id"), nullable=False)
    name: Mapped[str] = mapped_column(String(255), nullable=False)
    description: Mapped[Optional[str]] = mapped_column(Text, nullable=True)
    order_index: Mapped[int] = mapped_column(Integer, nullable=False)
    status: Mapped[str] = mapped_column(String(50), nullable=False, default=TaskStatus.PENDING)
    dependencies: Mapped[List[str]] = mapped_column(JSON, nullable=False, default=list)
    estimated_duration: Mapped[Optional[str]] = mapped_column(String(100), nullable=True)
    project_metadata: Mapped[Optional[Dict]] = mapped_column(JSON, nullable=True)
    created_at: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow)
    updated_at: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Relationships
    phase: Mapped["Phase"] = relationship("Phase", back_populates="steps")
    tasks: Mapped[List["Task"]] = relationship("Task", back_populates="step", cascade="all, delete-orphan")


class Task(Base):
    """Task entity - actionable items assigned to agents."""
    __tablename__ = "tasks"

    id: Mapped[str] = mapped_column(UUID(as_uuid=False), primary_key=True, default=lambda: str(uuid4()))
    step_id: Mapped[str] = mapped_column(UUID(as_uuid=False), ForeignKey("steps.id"), nullable=False)
    name: Mapped[str] = mapped_column(String(255), nullable=False)
    description: Mapped[Optional[str]] = mapped_column(Text, nullable=True)
    order_index: Mapped[int] = mapped_column(Integer, nullable=False)
    status: Mapped[str] = mapped_column(String(50), nullable=False, default=TaskStatus.PENDING)
    assigned_agent: Mapped[str] = mapped_column(String(50), nullable=False)
    dependencies: Mapped[List[str]] = mapped_column(JSON, nullable=False, default=list)
    artifacts: Mapped[List[Dict]] = mapped_column(JSON, nullable=False, default=list)
    estimated_duration: Mapped[Optional[str]] = mapped_column(String(100), nullable=True)
    started_at: Mapped[Optional[datetime]] = mapped_column(DateTime, nullable=True)
    completed_at: Mapped[Optional[datetime]] = mapped_column(DateTime, nullable=True)
    error_message: Mapped[Optional[str]] = mapped_column(Text, nullable=True)
    project_metadata: Mapped[Optional[Dict]] = mapped_column(JSON, nullable=True)
    created_at: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow)
    updated_at: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Relationships
    step: Mapped["Step"] = relationship("Step", back_populates="tasks")


class RoadmapVersion(Base):
    """Roadmap version history for comprehensive change tracking."""
    __tablename__ = "roadmap_versions"

    id: Mapped[str] = mapped_column(UUID(as_uuid=False), primary_key=True, default=lambda: str(uuid4()))
    roadmap_id: Mapped[str] = mapped_column(UUID(as_uuid=False), ForeignKey("roadmaps.id"), nullable=False)
    version_number: Mapped[str] = mapped_column(String(50), nullable=False)
    major_version: Mapped[int] = mapped_column(Integer, nullable=False, default=1)
    minor_version: Mapped[int] = mapped_column(Integer, nullable=False, default=0)
    patch_version: Mapped[int] = mapped_column(Integer, nullable=False, default=0)

    # Version metadata
    version_type: Mapped[str] = mapped_column(String(20), nullable=False, default="minor")  # major, minor, patch
    change_summary: Mapped[Optional[str]] = mapped_column(Text, nullable=True)
    change_details: Mapped[Optional[Dict]] = mapped_column(JSON, nullable=True)

    # Snapshot of roadmap structure at this version
    roadmap_snapshot: Mapped[Dict] = mapped_column(JSON, nullable=False)

    # User and context information
    created_by: Mapped[Optional[str]] = mapped_column(String(255), nullable=True)
    created_by_email: Mapped[Optional[str]] = mapped_column(String(255), nullable=True)
    creation_reason: Mapped[Optional[str]] = mapped_column(Text, nullable=True)

    # Approval and release information
    is_released: Mapped[bool] = mapped_column(Boolean, default=False)
    released_at: Mapped[Optional[datetime]] = mapped_column(DateTime, nullable=True)
    released_by: Mapped[Optional[str]] = mapped_column(String(255), nullable=True)
    release_notes: Mapped[Optional[str]] = mapped_column(Text, nullable=True)

    # Timestamps
    created_at: Mapped[datetime] = mapped_column(DateTime, default=lambda: datetime.now(timezone.utc))

    # Relationships
    roadmap: Mapped["Roadmap"] = relationship("Roadmap", back_populates="versions")


# Pydantic Schemas for API

class TaskArtifact(BaseModel):
    """Artifact produced by task execution."""
    type: Literal["code", "config", "documentation", "schema", "test"]
    filename: str
    content: str
    description: str
    project_metadata: Optional[Dict] = None


class TaskBase(BaseModel):
    """Base task schema."""
    name: str = Field(..., description="Task name")
    description: Optional[str] = None
    assigned_agent: AgentType
    dependencies: List[str] = Field(default_factory=list)
    estimated_duration: Optional[str] = None
    project_metadata: Optional[Dict] = None


class TaskCreate(TaskBase):
    """Schema for creating tasks."""
    order_index: int = Field(..., ge=0, description="Task order within step")


class TaskUpdate(BaseModel):
    """Schema for updating tasks."""
    name: Optional[str] = None
    description: Optional[str] = None
    status: Optional[TaskStatus] = None
    assigned_agent: Optional[AgentType] = None
    dependencies: Optional[List[str]] = None
    artifacts: Optional[List[TaskArtifact]] = None
    error_message: Optional[str] = None
    project_metadata: Optional[Dict] = None


class TaskResponse(TaskBase):
    """Schema for task responses."""
    id: str
    order_index: int
    status: TaskStatus
    artifacts: List[TaskArtifact] = Field(default_factory=list)
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    error_message: Optional[str] = None
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


class StepBase(BaseModel):
    """Base step schema."""
    name: str = Field(..., description="Step name")
    description: Optional[str] = None
    dependencies: List[str] = Field(default_factory=list)
    estimated_duration: Optional[str] = None
    project_metadata: Optional[Dict] = None


class StepCreate(StepBase):
    """Schema for creating steps."""
    order_index: int = Field(..., ge=0, description="Step order within phase")
    tasks: List[TaskCreate] = Field(default_factory=list)


class StepUpdate(BaseModel):
    """Schema for updating steps."""
    name: Optional[str] = None
    description: Optional[str] = None
    status: Optional[TaskStatus] = None
    dependencies: Optional[List[str]] = None
    project_metadata: Optional[Dict] = None


class StepResponse(StepBase):
    """Schema for step responses."""
    id: str
    order_index: int
    status: TaskStatus
    tasks: List[TaskResponse] = Field(default_factory=list)
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


class PhaseBase(BaseModel):
    """Base phase schema."""
    name: str = Field(..., description="Phase name")
    description: Optional[str] = None
    dependencies: List[str] = Field(default_factory=list)
    estimated_duration: Optional[str] = None
    project_metadata: Optional[Dict] = None


class PhaseCreate(PhaseBase):
    """Schema for creating phases."""
    order_index: int = Field(..., ge=0, description="Phase order within roadmap")
    steps: List[StepCreate] = Field(default_factory=list)


class PhaseUpdate(BaseModel):
    """Schema for updating phases."""
    name: Optional[str] = None
    description: Optional[str] = None
    status: Optional[TaskStatus] = None
    dependencies: Optional[List[str]] = None
    project_metadata: Optional[Dict] = None


class PhaseResponse(PhaseBase):
    """Schema for phase responses."""
    id: str
    order_index: int
    status: TaskStatus
    steps: List[StepResponse] = Field(default_factory=list)
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


class RoadmapBase(BaseModel):
    """Base roadmap schema."""
    name: str = Field(default="Project Roadmap", description="Roadmap name")
    version: str = Field(default="1.0.0", description="Roadmap version")
    project_metadata: Optional[Dict] = None


class RoadmapCreate(RoadmapBase):
    """Schema for creating roadmaps."""
    phases: List[PhaseCreate] = Field(default_factory=list)


class RoadmapUpdate(BaseModel):
    """Schema for updating roadmaps."""
    name: Optional[str] = None
    version: Optional[str] = None
    status: Optional[TaskStatus] = None
    project_metadata: Optional[Dict] = None


class RoadmapResponse(RoadmapBase):
    """Schema for roadmap responses."""
    id: str
    project_id: str
    status: TaskStatus
    phases: List[PhaseResponse] = Field(default_factory=list)
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


class ProjectBase(BaseModel):
    """Base project schema."""
    name: str = Field(..., description="Project name")
    description: Optional[str] = None
    tech_stack: Optional[Dict] = None
    project_rules: Optional[Dict] = None


class ProjectCreate(ProjectBase):
    """Schema for creating projects."""
    roadmap: Optional[RoadmapCreate] = None


class ProjectUpdate(BaseModel):
    """Schema for updating projects."""
    name: Optional[str] = None
    description: Optional[str] = None
    tech_stack: Optional[Dict] = None
    project_rules: Optional[Dict] = None


class ProjectResponse(ProjectBase):
    """Schema for project responses."""
    id: str
    roadmap: Optional[RoadmapResponse] = None
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


# Roadmap Versioning Schemas

class RoadmapVersionCreate(BaseModel):
    """Schema for creating roadmap versions."""
    version_type: Literal["major", "minor", "patch"] = "minor"
    change_summary: Optional[str] = None
    change_details: Optional[Dict] = None
    creation_reason: Optional[str] = None
    auto_increment: bool = True


class RoadmapVersionUpdate(BaseModel):
    """Schema for updating roadmap versions."""
    change_summary: Optional[str] = None
    change_details: Optional[Dict] = None
    is_released: Optional[bool] = None
    release_notes: Optional[str] = None


class RoadmapVersionResponse(BaseModel):
    """Schema for roadmap version responses."""
    id: str
    roadmap_id: str
    version_number: str
    major_version: int
    minor_version: int
    patch_version: int
    version_type: str
    change_summary: Optional[str] = None
    change_details: Optional[Dict] = None
    roadmap_snapshot: Dict
    created_by: Optional[str] = None
    created_by_email: Optional[str] = None
    creation_reason: Optional[str] = None
    is_released: bool
    released_at: Optional[datetime] = None
    released_by: Optional[str] = None
    release_notes: Optional[str] = None
    created_at: datetime

    class Config:
        from_attributes = True


class VersionComparison(BaseModel):
    """Schema for version comparison results."""
    from_version: str
    to_version: str
    changes: Dict[str, Any]
    added_items: List[Dict]
    removed_items: List[Dict]
    modified_items: List[Dict]
    summary: str
