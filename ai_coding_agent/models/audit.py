"""
Audit Trail System Models
Implements comprehensive audit logging for Phase B1 enhancements.
"""

from datetime import datetime
from typing import Dict, List, Optional, Any
from enum import Enum
from uuid import uuid4

from sqlalchemy import Column, String, Text, DateTime, Foreign<PERSON>ey, JSON, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>
from sqlalchemy.orm import relationship, Mapped, mapped_column
from sqlalchemy.dialects.postgresql import UUID
from pydantic import BaseModel, Field

from .base import Base


class AuditAction(str, Enum):
    """Types of audit actions."""
    CREATE = "create"
    UPDATE = "update"
    DELETE = "delete"
    STATUS_CHANGE = "status_change"
    ASSIGN_AGENT = "assign_agent"
    ADD_ARTIFACT = "add_artifact"
    DEPENDENCY_CHANGE = "dependency_change"
    VERSION_CHANGE = "version_change"


class AuditEntityType(str, Enum):
    """Types of entities that can be audited."""
    PROJECT = "project"
    ROADMAP = "roadmap"
    PHASE = "phase"
    STEP = "step"
    TASK = "task"


class AuditLog(Base):
    """Comprehensive audit log for all roadmap changes."""
    __tablename__ = "audit_logs"

    id: Mapped[str] = mapped_column(UUID(as_uuid=False), primary_key=True, default=lambda: str(uuid4()))
    entity_type: Mapped[str] = mapped_column(String(50), nullable=False)
    entity_id: Mapped[str] = mapped_column(UUID(as_uuid=False), nullable=False)
    action: Mapped[str] = mapped_column(String(50), nullable=False)
    user_id: Mapped[Optional[str]] = mapped_column(String(255), nullable=True)
    user_email: Mapped[Optional[str]] = mapped_column(String(255), nullable=True)

    # Change tracking
    old_values: Mapped[Optional[Dict]] = mapped_column(JSON, nullable=True)
    new_values: Mapped[Optional[Dict]] = mapped_column(JSON, nullable=True)
    changed_fields: Mapped[List[str]] = mapped_column(JSON, nullable=False, default=list)

    # Context information
    session_id: Mapped[Optional[str]] = mapped_column(String(255), nullable=True)
    ip_address: Mapped[Optional[str]] = mapped_column(String(45), nullable=True)
    user_agent: Mapped[Optional[str]] = mapped_column(Text, nullable=True)

    # Additional metadata
    reason: Mapped[Optional[str]] = mapped_column(Text, nullable=True)
    audit_metadata: Mapped[Optional[Dict]] = mapped_column(JSON, nullable=True)

    # Timestamps
    created_at: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow)


class StatusHistory(Base):
    """Detailed status change history for roadmap entities."""
    __tablename__ = "status_history"

    id: Mapped[str] = mapped_column(UUID(as_uuid=False), primary_key=True, default=lambda: str(uuid4()))
    entity_type: Mapped[str] = mapped_column(String(50), nullable=False)
    entity_id: Mapped[str] = mapped_column(UUID(as_uuid=False), nullable=False)

    # Status change details
    old_status: Mapped[Optional[str]] = mapped_column(String(50), nullable=True)
    new_status: Mapped[str] = mapped_column(String(50), nullable=False)

    # Change context
    user_id: Mapped[Optional[str]] = mapped_column(String(255), nullable=True)
    triggered_by: Mapped[str] = mapped_column(String(50), nullable=False, default="user")  # user, system, agent
    reason: Mapped[Optional[str]] = mapped_column(Text, nullable=True)

    # Duration tracking
    duration_in_previous_status: Mapped[Optional[int]] = mapped_column(Integer, nullable=True)  # seconds

    # Additional context
    status_metadata: Mapped[Optional[Dict]] = mapped_column(JSON, nullable=True)

    # Timestamps
    created_at: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow)


class ConcurrencyControl(Base):
    """Optimistic locking and conflict resolution for concurrent editing."""
    __tablename__ = "concurrency_control"

    id: Mapped[str] = mapped_column(UUID(as_uuid=False), primary_key=True, default=lambda: str(uuid4()))
    entity_type: Mapped[str] = mapped_column(String(50), nullable=False)
    entity_id: Mapped[str] = mapped_column(UUID(as_uuid=False), nullable=False)

    # Version control
    version: Mapped[int] = mapped_column(Integer, nullable=False, default=1)
    last_modified_by: Mapped[Optional[str]] = mapped_column(String(255), nullable=True)
    last_modified_at: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow)

    # Lock information
    is_locked: Mapped[bool] = mapped_column(Boolean, default=False)
    locked_by: Mapped[Optional[str]] = mapped_column(String(255), nullable=True)
    locked_at: Mapped[Optional[datetime]] = mapped_column(DateTime, nullable=True)
    lock_expires_at: Mapped[Optional[datetime]] = mapped_column(DateTime, nullable=True)

    # Conflict resolution
    has_conflicts: Mapped[bool] = mapped_column(Boolean, default=False)
    conflict_data: Mapped[Optional[Dict]] = mapped_column(JSON, nullable=True)

    # Timestamps
    created_at: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow)
    updated_at: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)


# Pydantic Schemas for API

class AuditLogResponse(BaseModel):
    """Schema for audit log responses."""
    id: str
    entity_type: AuditEntityType
    entity_id: str
    action: AuditAction
    user_id: Optional[str] = None
    user_email: Optional[str] = None
    old_values: Optional[Dict] = None
    new_values: Optional[Dict] = None
    changed_fields: List[str] = Field(default_factory=list)
    session_id: Optional[str] = None
    ip_address: Optional[str] = None
    user_agent: Optional[str] = None
    reason: Optional[str] = None
    audit_metadata: Optional[Dict] = None
    created_at: datetime

    class Config:
        from_attributes = True


class StatusHistoryResponse(BaseModel):
    """Schema for status history responses."""
    id: str
    entity_type: AuditEntityType
    entity_id: str
    old_status: Optional[str] = None
    new_status: str
    user_id: Optional[str] = None
    triggered_by: str
    reason: Optional[str] = None
    duration_in_previous_status: Optional[int] = None
    status_metadata: Optional[Dict] = None
    created_at: datetime

    class Config:
        from_attributes = True


class ConcurrencyControlResponse(BaseModel):
    """Schema for concurrency control responses."""
    id: str
    entity_type: AuditEntityType
    entity_id: str
    version: int
    last_modified_by: Optional[str] = None
    last_modified_at: datetime
    is_locked: bool
    locked_by: Optional[str] = None
    locked_at: Optional[datetime] = None
    lock_expires_at: Optional[datetime] = None
    has_conflicts: bool
    conflict_data: Optional[Dict] = None
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


class AuditLogFilter(BaseModel):
    """Filter parameters for audit log queries."""
    entity_type: Optional[AuditEntityType] = None
    entity_id: Optional[str] = None
    action: Optional[AuditAction] = None
    user_id: Optional[str] = None
    start_date: Optional[datetime] = None
    end_date: Optional[datetime] = None
    limit: int = Field(default=100, le=1000)
    offset: int = Field(default=0, ge=0)


class ConflictResolution(BaseModel):
    """Schema for conflict resolution requests."""
    entity_type: AuditEntityType
    entity_id: str
    resolution_strategy: str  # "merge", "overwrite", "manual"
    resolved_values: Dict[str, Any]
    user_id: str
    reason: Optional[str] = None
